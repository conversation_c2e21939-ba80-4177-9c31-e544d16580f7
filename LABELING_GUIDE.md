# 昆虫检测数据标注指南

## 概述

本指南将帮助您使用集成的LabelImg工具为昆虫检测项目进行数据标注。

## 快速开始

### 方法一：使用批处理脚本（推荐）

```bash
# Windows用户
start_labeling.bat

# 然后选择选项1：快速启动标注工具
```

### 方法二：使用Python脚本

```bash
# 快速启动
python start_labeling.py

# 或启动完整GUI界面
python labeling_tool.py
```

## 标注流程

### 1. 准备图片数据

将要标注的原始图片放入以下目录：
```
datasets/raw_images/
```

支持的图片格式：
- JPG/JPEG
- PNG
- BMP

### 2. 启动标注工具

运行启动脚本后，系统会：
- 自动检查并安装LabelImg
- 创建必要的目录结构
- 生成类别定义文件
- 启动LabelImg标注界面

### 3. 在LabelImg中进行标注

#### 基本操作：
- **创建边界框**：点击"Create RectBox"或按`W`键
- **选择类别**：在右侧类别列表中选择对应的昆虫类型
- **保存标注**：按`Ctrl+S`或点击"Save"
- **下一张图片**：按`D`键或点击"Next Image"
- **上一张图片**：按`A`键或点击"Prev Image"

#### 标注规范：
1. **边界框要求**：
   - 紧贴昆虫边缘，不要留太多空白
   - 完整包含昆虫身体，不要切割
   - 避免包含过多背景

2. **类别选择**：
   - 仔细识别昆虫种类
   - 如不确定，可参考类别说明
   - 每个昆虫单独标注

3. **质量标准**：
   - 确保边界框准确
   - 避免重复标注同一个昆虫
   - 标注完成后检查保存状态

### 4. 整理标注数据

标注完成后，运行数据整理工具：

```bash
python organize_dataset.py
```

这将：
- 验证标注文件格式
- 按比例分割数据集（训练/验证/测试）
- 生成数据集统计报告

## 支持的昆虫类别

| 索引 | 英文名称 | 中文名称 | 特征描述 |
|------|----------|----------|----------|
| 0 | army worm | 粘虫 | 重要的农业害虫，幼虫危害作物 |
| 1 | legume blister beetle | 豆芫菁 | 豆类作物害虫，成虫取食叶片 |
| 2 | red spider | 红蜘蛛 | 常见植物害虫，体型微小呈红色 |
| 3 | rice gall midge | 稻瘿蚊 | 水稻害虫，幼虫在稻茎内形成虫瘿 |
| 4 | rice leaf roller | 稻纵卷叶螟 | 水稻叶片害虫，幼虫卷叶取食 |
| 5 | rice leafhopper | 稻飞虱 | 水稻刺吸式害虫，传播病毒 |
| 6 | rice water weevil | 稻水象甲 | 水稻根部害虫，成虫取食叶片 |
| 7 | wheat phloeothrips | 麦长管蚜 | 小麦害虫，刺吸植物汁液 |
| 8 | white backed plant hopper | 白背飞虱 | 水稻害虫，背部有白色条纹 |
| 9 | yellow rice borer | 黄稻螟 | 水稻钻蛀性害虫，幼虫蛀食茎秆 |

## 目录结构

```
datasets/
├── raw_images/              # 原始图片（待标注）
├── predefined_classes/
│   └── classes.txt         # 类别定义文件
├── images/                 # 整理后的图片
│   ├── train/             # 训练集图片
│   ├── val/               # 验证集图片
│   └── test/              # 测试集图片
├── labels/                # 标注文件
│   ├── train/             # 训练集标签
│   ├── val/               # 验证集标签
│   └── test/              # 测试集标签
└── dataset_summary.json   # 数据集统计信息
```

## 工具说明

### 1. start_labeling.py
- 快速启动标注工具
- 自动环境检查和初始化
- 适合简单的标注任务

### 2. labeling_tool.py
- 完整的标注管理界面
- 提供数据集统计和管理功能
- 适合大规模标注项目

### 3. organize_dataset.py
- 数据集整理工具
- 验证标注质量
- 按比例分割数据集

### 4. check_annotations.py
- 标注质量检查工具
- 检测常见标注错误
- 生成质量报告

## 标注技巧

### 1. 提高标注效率
- 使用快捷键操作
- 批量处理相似图片
- 定期保存进度

### 2. 保证标注质量
- 仔细观察昆虫特征
- 参考标准样本
- 定期检查标注结果

### 3. 处理困难情况
- **模糊图片**：跳过或标注可见部分
- **重叠昆虫**：分别标注每个个体
- **部分遮挡**：标注可见部分
- **不确定类别**：查阅参考资料或跳过

## 质量控制

### 标注前检查
- [ ] 图片清晰度足够
- [ ] 昆虫特征明显
- [ ] 了解目标类别特征

### 标注中注意
- [ ] 边界框准确
- [ ] 类别选择正确
- [ ] 避免重复标注
- [ ] 定期保存

### 标注后验证
- [ ] 运行质量检查工具
- [ ] 查看统计报告
- [ ] 修复发现的问题

## 常见问题

### Q: LabelImg安装失败怎么办？
A: 
1. 检查Python环境
2. 手动安装：`pip install labelImg`
3. 使用conda：`conda install labelImg`

### Q: 标注文件保存在哪里？
A: 标注文件自动保存在图片同目录下，文件名相同但扩展名为.txt

### Q: 如何修改已有标注？
A: 在LabelImg中重新打开对应图片，修改边界框后保存

### Q: 数据集分割比例如何调整？
A: 在运行`organize_dataset.py`时可以自定义分割比例

### Q: 如何检查标注质量？
A: 运行`python check_annotations.py`生成质量报告

## 最佳实践

1. **标注前准备**
   - 熟悉所有昆虫类别
   - 准备参考图片
   - 设置舒适的工作环境

2. **标注过程中**
   - 保持专注，避免疲劳标注
   - 定期休息，保证标注质量
   - 遇到问题及时记录

3. **标注后处理**
   - 及时整理和备份数据
   - 运行质量检查工具
   - 生成数据集统计报告

4. **团队协作**
   - 制定统一的标注标准
   - 定期交流标注经验
   - 交叉检查标注质量

## 技术支持

如果在使用过程中遇到问题：

1. 查看控制台错误信息
2. 检查Python环境和依赖
3. 参考LabelImg官方文档
4. 运行系统诊断工具

---

**注意**：标注是机器学习项目中最重要的环节之一，高质量的标注数据直接影响模型的性能。请认真对待每一个标注任务！
