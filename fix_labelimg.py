# -*- coding: utf-8 -*-
"""
LabelImg启动修复脚本
解决LabelImg启动相关的问题
"""

import os
import sys
import subprocess
import shutil

def check_labelimg_installation():
    """检查LabelImg安装状态"""
    print("=" * 50)
    print("检查LabelImg安装状态")
    print("=" * 50)
    
    # 检查模块导入
    try:
        import labelImg
        print("✅ LabelImg模块可以导入")
        print(f"   安装路径: {labelImg.__file__}")
    except ImportError:
        print("❌ LabelImg模块无法导入")
        return False
    
    # 检查命令行可用性
    try:
        result = subprocess.run(["labelImg", "--help"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✅ labelImg命令可用")
            return True
        else:
            print("❌ labelImg命令不可用")
            print(f"   错误: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ labelImg命令未找到")
        return False
    except subprocess.TimeoutExpired:
        print("❌ labelImg命令超时")
        return False

def try_different_install_methods():
    """尝试不同的安装方法"""
    print("\n" + "=" * 50)
    print("尝试修复LabelImg安装")
    print("=" * 50)
    
    methods = [
        ("pip重新安装", ["pip", "uninstall", "labelImg", "-y"], ["pip", "install", "labelImg"]),
        ("pip指定版本", ["pip", "uninstall", "labelImg", "-y"], ["pip", "install", "labelImg==1.8.6"]),
        ("conda安装", ["conda", "uninstall", "labelImg", "-y"], ["conda", "install", "labelImg", "-y"]),
    ]
    
    for method_name, uninstall_cmd, install_cmd in methods:
        print(f"\n尝试方法: {method_name}")
        
        try:
            # 卸载
            print("  卸载旧版本...")
            subprocess.run(uninstall_cmd, capture_output=True, timeout=30)
            
            # 安装
            print("  安装新版本...")
            result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                print("  ✅ 安装成功")
                
                # 验证安装
                if check_labelimg_installation():
                    print(f"🎉 {method_name} 成功!")
                    return True
                else:
                    print(f"  ❌ 安装后验证失败")
            else:
                print(f"  ❌ 安装失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("  ❌ 安装超时")
        except FileNotFoundError:
            print(f"  ❌ 命令不存在: {install_cmd[0]}")
        except Exception as e:
            print(f"  ❌ 安装异常: {e}")
    
    return False

def create_alternative_launcher():
    """创建替代启动器"""
    print("\n" + "=" * 50)
    print("创建替代启动器")
    print("=" * 50)
    
    launcher_code = '''# -*- coding: utf-8 -*-
"""
LabelImg替代启动器
"""

import sys
import os

def main():
    try:
        # 方法1: 尝试直接导入并运行
        import labelImg.app
        from labelImg.app import main as labelimg_main
        
        # 设置命令行参数
        if len(sys.argv) > 1:
            sys.argv = ['labelImg'] + sys.argv[1:]
        else:
            sys.argv = ['labelImg']
        
        labelimg_main()
        
    except ImportError:
        print("错误: 无法导入LabelImg模块")
        print("请确保LabelImg已正确安装: pip install labelImg")
        sys.exit(1)
    except Exception as e:
        print(f"启动LabelImg时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    try:
        with open("labelimg_launcher.py", "w", encoding="utf-8") as f:
            f.write(launcher_code)
        
        print("✅ 替代启动器创建成功: labelimg_launcher.py")
        print("   使用方法: python labelimg_launcher.py [图片目录] [类别文件]")
        return True
        
    except Exception as e:
        print(f"❌ 创建启动器失败: {e}")
        return False

def update_project_scripts():
    """更新项目脚本以使用新的启动方式"""
    print("\n" + "=" * 50)
    print("更新项目脚本")
    print("=" * 50)
    
    # 检查是否可以直接使用labelImg命令
    try:
        result = subprocess.run(["labelImg", "--help"], 
                              capture_output=True, timeout=5)
        if result.returncode == 0:
            print("✅ 使用直接命令启动方式")
            return True
    except:
        pass
    
    # 如果直接命令不可用，使用替代启动器
    if os.path.exists("labelimg_launcher.py"):
        print("✅ 使用替代启动器")
        
        # 更新start_labeling.py
        try:
            with open("start_labeling.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # 替换启动命令
            old_cmd = 'cmd = ["labelImg", raw_images_dir, classes_file]'
            new_cmd = 'cmd = [sys.executable, "labelimg_launcher.py", raw_images_dir, classes_file]'
            
            if old_cmd in content:
                content = content.replace(old_cmd, new_cmd)
                
                with open("start_labeling.py", "w", encoding="utf-8") as f:
                    f.write(content)
                
                print("✅ start_labeling.py 已更新")
            
        except Exception as e:
            print(f"❌ 更新start_labeling.py失败: {e}")
        
        return True
    
    return False

def test_labelimg_startup():
    """测试LabelImg启动"""
    print("\n" + "=" * 50)
    print("测试LabelImg启动")
    print("=" * 50)
    
    # 确保测试目录存在
    test_dir = "datasets/raw_images"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建测试图片
    test_image = os.path.join(test_dir, "test.jpg")
    if not os.path.exists(test_image):
        try:
            import numpy as np
            from PIL import Image
            
            # 创建一个简单的测试图片
            img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
            img = Image.fromarray(img_array)
            img.save(test_image)
            print(f"✅ 创建测试图片: {test_image}")
            
        except Exception as e:
            print(f"⚠️  无法创建测试图片: {e}")
    
    # 确保类别文件存在
    classes_file = "datasets/predefined_classes/classes.txt"
    os.makedirs(os.path.dirname(classes_file), exist_ok=True)
    
    if not os.path.exists(classes_file):
        try:
            import Config
            with open(classes_file, 'w', encoding='utf-8') as f:
                for i in range(len(Config.names)):
                    f.write(f"{Config.names[i]}\n")
            print(f"✅ 创建类别文件: {classes_file}")
        except Exception as e:
            print(f"❌ 创建类别文件失败: {e}")
            return False
    
    # 测试启动
    test_methods = [
        ("直接命令", ["labelImg", test_dir, classes_file]),
        ("Python模块", [sys.executable, "-m", "labelImg", test_dir, classes_file]),
        ("替代启动器", [sys.executable, "labelimg_launcher.py", test_dir, classes_file])
    ]
    
    for method_name, cmd in test_methods:
        print(f"\n测试方法: {method_name}")
        print(f"命令: {' '.join(cmd)}")
        
        try:
            # 只测试命令是否能正常解析，不实际启动GUI
            if method_name == "替代启动器" and not os.path.exists("labelimg_launcher.py"):
                print("  ⚠️  启动器文件不存在")
                continue
            
            # 对于GUI程序，我们只检查命令是否存在
            if method_name == "直接命令":
                result = subprocess.run(["labelImg", "--help"], 
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    print("  ✅ 命令可用")
                    return True
                else:
                    print("  ❌ 命令不可用")
            else:
                print("  ⚠️  需要手动测试GUI启动")
                
        except FileNotFoundError:
            print("  ❌ 命令未找到")
        except subprocess.TimeoutExpired:
            print("  ❌ 命令超时")
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("LabelImg启动修复工具")
    print("=" * 50)
    
    # 检查当前状态
    if check_labelimg_installation():
        print("\n🎉 LabelImg已正确安装并可用!")
        
        # 测试启动
        if test_labelimg_startup():
            print("\n✅ 所有测试通过，LabelImg可以正常使用")
            return
    
    print("\n⚠️  检测到LabelImg问题，开始修复...")
    
    # 尝试修复
    if try_different_install_methods():
        print("\n🎉 LabelImg修复成功!")
        return
    
    print("\n⚠️  标准修复方法失败，创建替代方案...")
    
    # 创建替代启动器
    if create_alternative_launcher():
        if update_project_scripts():
            print("\n🎉 替代方案创建成功!")
            print("现在可以使用以下方式启动:")
            print("1. python start_labeling.py")
            print("2. python labelimg_launcher.py [图片目录] [类别文件]")
            return
    
    print("\n❌ 所有修复方法都失败了")
    print("\n建议手动解决:")
    print("1. 完全卸载Python和所有包")
    print("2. 重新安装Python 3.8+")
    print("3. 重新安装依赖: pip install labelImg PyQt5")
    print("4. 或者使用在线标注工具如Roboflow")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
