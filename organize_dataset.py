# -*- coding: utf-8 -*-
"""
数据集整理工具
将标注好的数据按比例分配到训练/验证/测试集
"""

import os
import shutil
import random
import json
from collections import defaultdict
import Config

class DatasetOrganizer:
    """数据集整理器"""
    
    def __init__(self):
        self.raw_images_dir = "datasets/raw_images"
        self.images_dir = "datasets/images"
        self.labels_dir = "datasets/labels"
        
        # 默认分割比例
        self.split_ratios = {
            'train': 0.7,  # 70%
            'val': 0.2,    # 20%
            'test': 0.1    # 10%
        }
    
    def scan_annotated_data(self):
        """扫描已标注的数据"""
        print("🔍 扫描已标注的数据...")
        
        if not os.path.exists(self.raw_images_dir):
            print(f"❌ 原始图片目录不存在: {self.raw_images_dir}")
            return []
        
        # 获取所有图片文件
        image_files = []
        for filename in os.listdir(self.raw_images_dir):
            if filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                image_path = os.path.join(self.raw_images_dir, filename)
                
                # 检查对应的标注文件
                name_without_ext = os.path.splitext(filename)[0]
                label_file = f"{name_without_ext}.txt"
                label_path = os.path.join(self.raw_images_dir, label_file)
                
                if os.path.exists(label_path):
                    image_files.append({
                        'image': filename,
                        'label': label_file,
                        'image_path': image_path,
                        'label_path': label_path
                    })
        
        print(f"📊 找到 {len(image_files)} 对已标注的图片和标签文件")
        return image_files
    
    def validate_annotations(self, annotated_files):
        """验证标注文件格式"""
        print("✅ 验证标注文件格式...")
        
        valid_files = []
        invalid_files = []
        class_stats = defaultdict(int)
        
        for file_info in annotated_files:
            try:
                with open(file_info['label_path'], 'r') as f:
                    lines = f.readlines()
                
                valid_annotations = 0
                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) != 5:
                        print(f"⚠️  {file_info['label']}: 第{line_num}行格式错误")
                        continue
                    
                    try:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        
                        # 检查类别ID是否有效
                        if class_id not in Config.names:
                            print(f"⚠️  {file_info['label']}: 无效的类别ID {class_id}")
                            continue
                        
                        # 检查坐标范围
                        if not (0 <= x_center <= 1 and 0 <= y_center <= 1 and 
                               0 < width <= 1 and 0 < height <= 1):
                            print(f"⚠️  {file_info['label']}: 坐标超出范围")
                            continue
                        
                        class_stats[class_id] += 1
                        valid_annotations += 1
                        
                    except ValueError:
                        print(f"⚠️  {file_info['label']}: 第{line_num}行数值格式错误")
                        continue
                
                if valid_annotations > 0:
                    file_info['annotation_count'] = valid_annotations
                    valid_files.append(file_info)
                else:
                    invalid_files.append(file_info)
                    
            except Exception as e:
                print(f"❌ 读取标注文件失败 {file_info['label']}: {e}")
                invalid_files.append(file_info)
        
        print(f"✅ 验证完成: {len(valid_files)} 个有效文件, {len(invalid_files)} 个无效文件")
        
        # 显示类别统计
        if class_stats:
            print("\n📊 类别统计:")
            for class_id, count in sorted(class_stats.items()):
                class_name = Config.CH_names.get(class_id, "未知")
                print(f"  {class_id}: {class_name} - {count} 个标注")
        
        return valid_files, invalid_files, class_stats
    
    def split_dataset(self, valid_files, ratios=None):
        """按比例分割数据集"""
        if ratios is None:
            ratios = self.split_ratios
        
        print(f"📂 按比例分割数据集: 训练{ratios['train']*100:.0f}% / 验证{ratios['val']*100:.0f}% / 测试{ratios['test']*100:.0f}%")
        
        # 随机打乱文件列表
        random.shuffle(valid_files)
        
        total_files = len(valid_files)
        train_count = int(total_files * ratios['train'])
        val_count = int(total_files * ratios['val'])
        test_count = total_files - train_count - val_count
        
        # 分割文件
        splits = {
            'train': valid_files[:train_count],
            'val': valid_files[train_count:train_count + val_count],
            'test': valid_files[train_count + val_count:]
        }
        
        print(f"📊 分割结果: 训练集{len(splits['train'])} / 验证集{len(splits['val'])} / 测试集{len(splits['test'])}")
        
        return splits
    
    def copy_files_to_splits(self, splits):
        """将文件复制到对应的分割目录"""
        print("📁 复制文件到目标目录...")
        
        # 确保目标目录存在
        for split in ['train', 'val', 'test']:
            os.makedirs(os.path.join(self.images_dir, split), exist_ok=True)
            os.makedirs(os.path.join(self.labels_dir, split), exist_ok=True)
        
        # 复制文件
        for split_name, files in splits.items():
            print(f"  正在处理 {split_name} 集...")
            
            for file_info in files:
                # 复制图片
                src_img = file_info['image_path']
                dst_img = os.path.join(self.images_dir, split_name, file_info['image'])
                shutil.copy2(src_img, dst_img)
                
                # 复制标签
                src_label = file_info['label_path']
                dst_label = os.path.join(self.labels_dir, split_name, file_info['label'])
                shutil.copy2(src_label, dst_label)
            
            print(f"    ✅ {split_name} 集: {len(files)} 个文件")
        
        print("✅ 文件复制完成")
    
    def generate_summary_report(self, splits, class_stats):
        """生成数据集摘要报告"""
        print("📋 生成数据集摘要报告...")
        
        report = {
            'dataset_info': {
                'total_files': sum(len(files) for files in splits.values()),
                'total_annotations': sum(class_stats.values()),
                'classes_count': len(class_stats),
                'split_ratios': self.split_ratios
            },
            'splits': {},
            'class_distribution': {}
        }
        
        # 统计各分割的信息
        for split_name, files in splits.items():
            report['splits'][split_name] = {
                'file_count': len(files),
                'annotation_count': sum(f.get('annotation_count', 0) for f in files)
            }
        
        # 类别分布
        for class_id, count in class_stats.items():
            class_name = Config.names.get(class_id, f"unknown_{class_id}")
            class_name_zh = Config.CH_names.get(class_id, "未知")
            report['class_distribution'][class_id] = {
                'name': class_name,
                'name_zh': class_name_zh,
                'count': count
            }
        
        # 保存报告
        report_file = "datasets/dataset_summary.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 摘要报告已保存: {report_file}")
        
        # 打印摘要
        print("\n" + "=" * 50)
        print("数据集摘要")
        print("=" * 50)
        print(f"总文件数: {report['dataset_info']['total_files']}")
        print(f"总标注数: {report['dataset_info']['total_annotations']}")
        print(f"类别数量: {report['dataset_info']['classes_count']}")
        
        print(f"\n分割统计:")
        for split_name, info in report['splits'].items():
            print(f"  {split_name}: {info['file_count']} 文件, {info['annotation_count']} 标注")
        
        print(f"\n类别分布:")
        for class_id, info in report['class_distribution'].items():
            print(f"  {class_id}: {info['name_zh']} ({info['name']}) - {info['count']} 个")
        
        return report
    
    def organize(self, ratios=None):
        """执行完整的数据集整理流程"""
        print("🚀 开始数据集整理...")
        
        # 1. 扫描已标注数据
        annotated_files = self.scan_annotated_data()
        if not annotated_files:
            print("❌ 没有找到已标注的数据")
            return False
        
        # 2. 验证标注格式
        valid_files, invalid_files, class_stats = self.validate_annotations(annotated_files)
        if not valid_files:
            print("❌ 没有有效的标注文件")
            return False
        
        # 3. 分割数据集
        splits = self.split_dataset(valid_files, ratios)
        
        # 4. 复制文件
        self.copy_files_to_splits(splits)
        
        # 5. 生成报告
        self.generate_summary_report(splits, class_stats)
        
        print("🎉 数据集整理完成!")
        return True

def main():
    """主函数"""
    print("昆虫检测数据集整理工具")
    print("=" * 40)
    
    organizer = DatasetOrganizer()
    
    # 检查是否有自定义分割比例
    print("📊 当前分割比例:")
    for split, ratio in organizer.split_ratios.items():
        print(f"  {split}: {ratio*100:.0f}%")
    
    response = input("\n是否使用默认分割比例? (y/n): ").lower()
    
    if response != 'y':
        try:
            train_ratio = float(input("训练集比例 (0-1): "))
            val_ratio = float(input("验证集比例 (0-1): "))
            test_ratio = 1.0 - train_ratio - val_ratio
            
            if test_ratio < 0:
                print("❌ 比例总和不能超过1")
                return
            
            organizer.split_ratios = {
                'train': train_ratio,
                'val': val_ratio,
                'test': test_ratio
            }
            
            print(f"✅ 使用自定义比例: 训练{train_ratio*100:.0f}% / 验证{val_ratio*100:.0f}% / 测试{test_ratio*100:.0f}%")
            
        except ValueError:
            print("❌ 输入格式错误，使用默认比例")
    
    # 执行整理
    print("\n开始整理数据集...")
    success = organizer.organize()
    
    if success:
        print("\n✅ 数据集整理完成!")
        print("💡 现在可以使用整理好的数据集进行训练")
        print("💡 运行 'python train.py' 开始训练模型")
    else:
        print("\n❌ 数据集整理失败")

if __name__ == "__main__":
    main()
