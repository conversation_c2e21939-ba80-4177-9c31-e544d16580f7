# -*- coding: utf-8 -*-
"""
快速启动LabelImg标注工具
简化版本的标注工具启动器
"""

import os
import sys
import subprocess
import Config

def check_labelimg_installation():
    """检查LabelImg是否已安装"""
    try:
        result = subprocess.run([sys.executable, "-c", "import labelImg"], 
                              capture_output=True, text=True)
        return result.returncode == 0
    except:
        return False

def install_labelimg():
    """安装LabelImg"""
    print("正在安装LabelImg...")
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", "labelImg"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ LabelImg安装成功")
            return True
        else:
            print(f"❌ 安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 安装异常: {e}")
        return False

def create_directories():
    """创建必要的目录"""
    dirs = [
        "datasets/raw_images",
        "datasets/images/train",
        "datasets/images/val", 
        "datasets/images/test",
        "datasets/labels/train",
        "datasets/labels/val",
        "datasets/labels/test",
        "datasets/predefined_classes"
    ]
    
    for dir_path in dirs:
        os.makedirs(dir_path, exist_ok=True)
    
    print("✅ 目录结构创建完成")

def create_classes_file():
    """创建类别定义文件"""
    classes_file = "datasets/predefined_classes/classes.txt"
    
    with open(classes_file, 'w', encoding='utf-8') as f:
        for i in range(len(Config.names)):
            class_name = Config.names[i]
            f.write(f"{class_name}\n")
    
    print(f"✅ 类别文件创建完成: {classes_file}")
    print("支持的类别:")
    for i, (idx, name) in enumerate(Config.names.items()):
        print(f"  {idx}: {name} ({Config.CH_names[idx]})")
    
    return classes_file

def start_labelimg():
    """启动LabelImg"""
    # 检查原始图片目录
    raw_images_dir = "datasets/raw_images"
    if not os.path.exists(raw_images_dir):
        print(f"❌ 图片目录不存在: {raw_images_dir}")
        print("请先将要标注的图片放入该目录")
        return False
    
    # 检查是否有图片
    image_files = [f for f in os.listdir(raw_images_dir) 
                   if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))]
    
    if not image_files:
        print(f"❌ 图片目录为空: {raw_images_dir}")
        print("请先将要标注的图片放入该目录")
        return False
    
    print(f"📁 找到 {len(image_files)} 张图片待标注")
    
    # 创建类别文件
    classes_file = create_classes_file()
    
    # 启动LabelImg
    try:
        print("🚀 正在启动LabelImg...")
        
        # 构建启动命令 - 直接使用labelImg命令
        cmd = ["labelImg", raw_images_dir, classes_file]

        print(f"执行命令: {' '.join(cmd)}")

        # 启动LabelImg
        process = subprocess.Popen(cmd)
        
        print("✅ LabelImg已启动!")
        print("\n📝 标注指南:")
        print("1. 使用矩形框工具标注昆虫位置")
        print("2. 选择正确的昆虫类别")
        print("3. 确保标注框紧贴昆虫边缘")
        print("4. 保存标注文件(Ctrl+S)")
        print("5. 标注完成后关闭LabelImg")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动LabelImg失败: {e}")
        return False

def show_usage():
    """显示使用说明"""
    print("=" * 60)
    print("昆虫检测数据标注工具")
    print("=" * 60)
    print("\n📋 使用步骤:")
    print("1. 将要标注的图片放入 datasets/raw_images/ 目录")
    print("2. 运行此脚本启动LabelImg")
    print("3. 在LabelImg中进行标注")
    print("4. 标注完成后整理数据到训练集")
    
    print(f"\n🐛 支持的昆虫类别 (共{len(Config.names)}种):")
    for idx, name in Config.names.items():
        print(f"  {idx}: {Config.CH_names[idx]} ({name})")
    
    print("\n📁 目录结构:")
    print("datasets/")
    print("├── raw_images/          # 原始图片(待标注)")
    print("├── images/")
    print("│   ├── train/          # 训练集图片")
    print("│   ├── val/            # 验证集图片")
    print("│   └── test/           # 测试集图片")
    print("├── labels/")
    print("│   ├── train/          # 训练集标签")
    print("│   ├── val/            # 验证集标签")
    print("│   └── test/           # 测试集标签")
    print("└── predefined_classes/")
    print("    └── classes.txt     # 类别定义文件")

def main():
    """主函数"""
    print("昆虫检测数据标注工具启动器")
    print("=" * 40)
    
    # 显示使用说明
    show_usage()
    
    # 创建目录结构
    print("\n🔧 初始化环境...")
    create_directories()
    
    # 检查LabelImg安装
    print("\n🔍 检查LabelImg安装状态...")
    if not check_labelimg_installation():
        print("⚠️  LabelImg未安装，正在安装...")
        if not install_labelimg():
            print("❌ 安装失败，请手动安装: pip install labelImg")
            return
    else:
        print("✅ LabelImg已安装")
    
    # 检查图片目录
    raw_images_dir = "datasets/raw_images"
    if os.path.exists(raw_images_dir):
        image_count = len([f for f in os.listdir(raw_images_dir) 
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp'))])
        if image_count > 0:
            print(f"\n📸 发现 {image_count} 张待标注图片")
            
            # 询问是否启动
            response = input("\n是否立即启动LabelImg进行标注? (y/n): ").lower()
            if response == 'y':
                start_labelimg()
            else:
                print("💡 准备好后运行此脚本启动标注工具")
        else:
            print(f"\n📂 图片目录为空: {raw_images_dir}")
            print("💡 请将要标注的图片放入该目录后重新运行")
    
    print("\n✅ 环境准备完成!")

if __name__ == "__main__":
    main()
