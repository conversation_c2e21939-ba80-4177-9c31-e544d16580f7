# -*- coding: utf-8 -*-
"""
标注质量检查工具
检查标注文件的质量和一致性
"""

import os
import cv2
import numpy as np
from collections import defaultdict, Counter
import matplotlib.pyplot as plt
import Config

class AnnotationChecker:
    """标注质量检查器"""
    
    def __init__(self):
        self.issues = []
        self.stats = defaultdict(int)
        
    def check_annotation_file(self, image_path, label_path):
        """检查单个标注文件"""
        issues = []
        
        # 检查文件是否存在
        if not os.path.exists(image_path):
            issues.append(f"图片文件不存在: {image_path}")
            return issues
        
        if not os.path.exists(label_path):
            issues.append(f"标注文件不存在: {label_path}")
            return issues
        
        # 读取图片信息
        try:
            image = cv2.imread(image_path)
            if image is None:
                issues.append(f"无法读取图片: {image_path}")
                return issues
            
            img_height, img_width = image.shape[:2]
        except Exception as e:
            issues.append(f"读取图片失败: {e}")
            return issues
        
        # 检查标注文件
        try:
            with open(label_path, 'r') as f:
                lines = f.readlines()
            
            if not lines:
                issues.append("标注文件为空")
                return issues
            
            annotations = []
            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue
                
                parts = line.split()
                if len(parts) != 5:
                    issues.append(f"第{line_num}行格式错误: 应为5个数值")
                    continue
                
                try:
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # 检查类别ID
                    if class_id not in Config.names:
                        issues.append(f"第{line_num}行: 无效的类别ID {class_id}")
                        continue
                    
                    # 检查坐标范围
                    if not (0 <= x_center <= 1):
                        issues.append(f"第{line_num}行: x_center超出范围 [0,1]: {x_center}")
                    
                    if not (0 <= y_center <= 1):
                        issues.append(f"第{line_num}行: y_center超出范围 [0,1]: {y_center}")
                    
                    if not (0 < width <= 1):
                        issues.append(f"第{line_num}行: width超出范围 (0,1]: {width}")
                    
                    if not (0 < height <= 1):
                        issues.append(f"第{line_num}行: height超出范围 (0,1]: {height}")
                    
                    # 检查边界框是否超出图像边界
                    x1 = x_center - width / 2
                    y1 = y_center - height / 2
                    x2 = x_center + width / 2
                    y2 = y_center + height / 2
                    
                    if x1 < 0 or y1 < 0 or x2 > 1 or y2 > 1:
                        issues.append(f"第{line_num}行: 边界框超出图像范围")
                    
                    # 检查边界框大小
                    pixel_width = width * img_width
                    pixel_height = height * img_height
                    area = pixel_width * pixel_height
                    
                    if area < 100:  # 小于100像素
                        issues.append(f"第{line_num}行: 边界框过小 ({area:.0f}像素)")
                    
                    if area > img_width * img_height * 0.8:  # 超过图像80%
                        issues.append(f"第{line_num}行: 边界框过大 ({area/(img_width*img_height)*100:.1f}%)")
                    
                    annotations.append({
                        'class_id': class_id,
                        'bbox': [x_center, y_center, width, height],
                        'area': area
                    })
                    
                except ValueError as e:
                    issues.append(f"第{line_num}行: 数值格式错误 - {e}")
                    continue
            
            # 检查重叠的边界框
            if len(annotations) > 1:
                overlaps = self.check_bbox_overlaps(annotations)
                if overlaps:
                    issues.extend(overlaps)
            
        except Exception as e:
            issues.append(f"读取标注文件失败: {e}")
        
        return issues
    
    def check_bbox_overlaps(self, annotations):
        """检查边界框重叠"""
        issues = []
        
        for i in range(len(annotations)):
            for j in range(i + 1, len(annotations)):
                bbox1 = annotations[i]['bbox']
                bbox2 = annotations[j]['bbox']
                
                iou = self.calculate_iou(bbox1, bbox2)
                if iou > 0.5:  # 重叠超过50%
                    issues.append(f"边界框重叠过多: IoU={iou:.2f}")
        
        return issues
    
    def calculate_iou(self, bbox1, bbox2):
        """计算两个边界框的IoU"""
        # 转换为角点坐标
        x1_1 = bbox1[0] - bbox1[2] / 2
        y1_1 = bbox1[1] - bbox1[3] / 2
        x2_1 = bbox1[0] + bbox1[2] / 2
        y2_1 = bbox1[1] + bbox1[3] / 2
        
        x1_2 = bbox2[0] - bbox2[2] / 2
        y1_2 = bbox2[1] - bbox2[3] / 2
        x2_2 = bbox2[0] + bbox2[2] / 2
        y2_2 = bbox2[1] + bbox2[3] / 2
        
        # 计算交集
        x1_inter = max(x1_1, x1_2)
        y1_inter = max(y1_1, y1_2)
        x2_inter = min(x2_1, x2_2)
        y2_inter = min(y2_1, y2_2)
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        # 计算并集
        area1 = bbox1[2] * bbox1[3]
        area2 = bbox2[2] * bbox2[3]
        union_area = area1 + area2 - inter_area
        
        return inter_area / union_area if union_area > 0 else 0.0
    
    def check_dataset(self, dataset_path):
        """检查整个数据集"""
        print(f"🔍 检查数据集: {dataset_path}")
        
        images_dir = os.path.join(dataset_path, "images")
        labels_dir = os.path.join(dataset_path, "labels")
        
        all_issues = {}
        class_counts = defaultdict(int)
        file_counts = {'total': 0, 'with_issues': 0, 'without_labels': 0}
        
        for split in ['train', 'val', 'test']:
            split_images_dir = os.path.join(images_dir, split)
            split_labels_dir = os.path.join(labels_dir, split)
            
            if not os.path.exists(split_images_dir):
                continue
            
            print(f"  检查 {split} 集...")
            
            for filename in os.listdir(split_images_dir):
                if not filename.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp')):
                    continue
                
                file_counts['total'] += 1
                
                image_path = os.path.join(split_images_dir, filename)
                label_filename = os.path.splitext(filename)[0] + '.txt'
                label_path = os.path.join(split_labels_dir, label_filename)
                
                if not os.path.exists(label_path):
                    file_counts['without_labels'] += 1
                    all_issues[f"{split}/{filename}"] = ["缺少标注文件"]
                    continue
                
                # 检查标注质量
                issues = self.check_annotation_file(image_path, label_path)
                
                if issues:
                    file_counts['with_issues'] += 1
                    all_issues[f"{split}/{filename}"] = issues
                
                # 统计类别
                try:
                    with open(label_path, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line:
                                class_id = int(line.split()[0])
                                if class_id in Config.names:
                                    class_counts[class_id] += 1
                except:
                    pass
        
        return all_issues, class_counts, file_counts
    
    def generate_report(self, all_issues, class_counts, file_counts):
        """生成检查报告"""
        print("\n" + "=" * 60)
        print("标注质量检查报告")
        print("=" * 60)
        
        # 总体统计
        print(f"\n📊 总体统计:")
        print(f"  总文件数: {file_counts['total']}")
        print(f"  有问题的文件: {file_counts['with_issues']}")
        print(f"  缺少标注的文件: {file_counts['without_labels']}")
        print(f"  正常文件: {file_counts['total'] - file_counts['with_issues'] - file_counts['without_labels']}")
        
        # 类别分布
        if class_counts:
            print(f"\n🐛 类别分布:")
            total_annotations = sum(class_counts.values())
            for class_id in sorted(class_counts.keys()):
                count = class_counts[class_id]
                percentage = count / total_annotations * 100
                class_name = Config.CH_names.get(class_id, "未知")
                print(f"  {class_id}: {class_name} - {count} 个 ({percentage:.1f}%)")
            
            print(f"\n  总标注数: {total_annotations}")
        
        # 问题详情
        if all_issues:
            print(f"\n❌ 问题详情:")
            issue_types = defaultdict(int)
            
            for filename, issues in all_issues.items():
                print(f"\n  📁 {filename}:")
                for issue in issues:
                    print(f"    - {issue}")
                    # 统计问题类型
                    if "格式错误" in issue:
                        issue_types["格式错误"] += 1
                    elif "超出范围" in issue:
                        issue_types["坐标超出范围"] += 1
                    elif "过小" in issue:
                        issue_types["边界框过小"] += 1
                    elif "过大" in issue:
                        issue_types["边界框过大"] += 1
                    elif "重叠" in issue:
                        issue_types["边界框重叠"] += 1
                    elif "缺少标注" in issue:
                        issue_types["缺少标注文件"] += 1
                    else:
                        issue_types["其他问题"] += 1
            
            print(f"\n📈 问题类型统计:")
            for issue_type, count in issue_types.items():
                print(f"  {issue_type}: {count} 次")
        
        # 质量评分
        if file_counts['total'] > 0:
            quality_score = (file_counts['total'] - file_counts['with_issues'] - file_counts['without_labels']) / file_counts['total'] * 100
            print(f"\n⭐ 数据集质量评分: {quality_score:.1f}%")
            
            if quality_score >= 90:
                print("✅ 数据集质量优秀")
            elif quality_score >= 80:
                print("⚠️  数据集质量良好，建议修复部分问题")
            elif quality_score >= 70:
                print("⚠️  数据集质量一般，需要修复较多问题")
            else:
                print("❌ 数据集质量较差，建议重新检查标注")

def main():
    """主函数"""
    print("昆虫检测标注质量检查工具")
    print("=" * 40)
    
    checker = AnnotationChecker()
    
    # 检查数据集
    dataset_path = "datasets"
    
    if not os.path.exists(dataset_path):
        print(f"❌ 数据集目录不存在: {dataset_path}")
        return
    
    all_issues, class_counts, file_counts = checker.check_dataset(dataset_path)
    
    # 生成报告
    checker.generate_report(all_issues, class_counts, file_counts)
    
    # 保存详细报告
    if all_issues:
        report_file = "datasets/annotation_check_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("标注质量检查详细报告\n")
            f.write("=" * 40 + "\n\n")
            
            for filename, issues in all_issues.items():
                f.write(f"文件: {filename}\n")
                for issue in issues:
                    f.write(f"  - {issue}\n")
                f.write("\n")
        
        print(f"\n📄 详细报告已保存: {report_file}")

if __name__ == "__main__":
    main()
