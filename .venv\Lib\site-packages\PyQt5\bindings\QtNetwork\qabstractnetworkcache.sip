// qabstractnetworkcache.sip generated by MetaSIP
//
// This file is part of the QtNetwork Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QNetworkCacheMetaData
{
%TypeHeaderCode
#include <qabstractnetworkcache.h>
%End

    typedef QList<QPair<QByteArray, QByteArray>> RawHeaderList;
    typedef QHash<QNetworkRequest::Attribute, QVariant> AttributesMap;

public:
    QNetworkCacheMetaData();
    QNetworkCacheMetaData(const QNetworkCacheMetaData &other);
    ~QNetworkCacheMetaData();
    bool operator==(const QNetworkCacheMetaData &other) const;
    bool operator!=(const QNetworkCacheMetaData &other) const;
    bool isValid() const;
    QUrl url() const;
    void setUrl(const QUrl &url);
    QNetworkCacheMetaData::RawHeaderList rawHeaders() const;
    void setRawHeaders(const QNetworkCacheMetaData::RawHeaderList &headers);
    QDateTime lastModified() const;
    void setLastModified(const QDateTime &dateTime);
    QDateTime expirationDate() const;
    void setExpirationDate(const QDateTime &dateTime);
    bool saveToDisk() const;
    void setSaveToDisk(bool allow);
    QNetworkCacheMetaData::AttributesMap attributes() const;
    void setAttributes(const QNetworkCacheMetaData::AttributesMap &attributes);
    void swap(QNetworkCacheMetaData &other /Constrained/);
};

QDataStream &operator<<(QDataStream &, const QNetworkCacheMetaData & /Constrained/) /ReleaseGIL/;
QDataStream &operator>>(QDataStream &, QNetworkCacheMetaData & /Constrained/) /ReleaseGIL/;

class QAbstractNetworkCache : QObject
{
%TypeHeaderCode
#include <qabstractnetworkcache.h>
%End

public:
    virtual ~QAbstractNetworkCache();
    virtual QNetworkCacheMetaData metaData(const QUrl &url) = 0;
    virtual void updateMetaData(const QNetworkCacheMetaData &metaData) = 0;
    virtual QIODevice *data(const QUrl &url) = 0 /Factory/;
    virtual bool remove(const QUrl &url) = 0;
    virtual qint64 cacheSize() const = 0;
    virtual QIODevice *prepare(const QNetworkCacheMetaData &metaData) = 0;
    virtual void insert(QIODevice *device) = 0;

public slots:
    virtual void clear() = 0;

protected:
    explicit QAbstractNetworkCache(QObject *parent /TransferThis/ = 0);
};
