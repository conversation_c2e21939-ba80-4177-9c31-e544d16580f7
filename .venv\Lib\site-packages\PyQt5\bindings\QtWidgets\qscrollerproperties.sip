// qscrollerproperties.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QScrollerProperties
{
%TypeHeaderCode
#include <qscrollerproperties.h>
%End

public:
    QScrollerProperties();
    QScrollerProperties(const QScrollerProperties &sp);
    virtual ~QScrollerProperties();
    bool operator==(const QScrollerProperties &sp) const;
    bool operator!=(const QScrollerProperties &sp) const;
    static void setDefaultScrollerProperties(const QScrollerProperties &sp);
    static void unsetDefaultScrollerProperties();

    enum OvershootPolicy
    {
        OvershootWhenScrollable,
        OvershootAlwaysOff,
        OvershootAlwaysOn,
    };

    enum FrameRates
    {
        Standard,
        Fps60,
        Fps30,
        Fps20,
    };

    enum ScrollMetric
    {
        MousePressEventDelay,
        DragStartDistance,
        DragVelocitySmoothingFactor,
        AxisLockThreshold,
        ScrollingCurve,
        DecelerationFactor,
        MinimumVelocity,
        MaximumVelocity,
        MaximumClickThroughVelocity,
        AcceleratingFlickMaximumTime,
        AcceleratingFlickSpeedupFactor,
        SnapPositionRatio,
        SnapTime,
        OvershootDragResistanceFactor,
        OvershootDragDistanceFactor,
        OvershootScrollDistanceFactor,
        OvershootScrollTime,
        HorizontalOvershootPolicy,
        VerticalOvershootPolicy,
        FrameRate,
        ScrollMetricCount,
    };

    QVariant scrollMetric(QScrollerProperties::ScrollMetric metric) const;
    void setScrollMetric(QScrollerProperties::ScrollMetric metric, const QVariant &value);
};
