// qvideosurfaceformat.sip generated by MetaSIP
//
// This file is part of the QtMultimedia Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QVideoSurfaceFormat
{
%TypeHeaderCode
#include <qvideosurfaceformat.h>
%End

public:
    enum Direction
    {
        TopToBottom,
        BottomToTop,
    };

    enum YCbCrColorSpace
    {
        YCbCr_Undefined,
        YCbCr_BT601,
        YCbCr_BT709,
        YCbCr_xvYCC601,
        YCbCr_xvYCC709,
        YCbCr_JPEG,
    };

    QVideoSurfaceFormat();
    QVideoSurfaceFormat(const QSize &size, QVideoFrame::PixelFormat format, QAbstractVideoBuffer::HandleType type = QAbstractVideoBuffer::NoHandle);
    QVideoSurfaceFormat(const QVideoSurfaceFormat &format);
    ~QVideoSurfaceFormat();
    bool operator==(const QVideoSurfaceFormat &format) const;
    bool operator!=(const QVideoSurfaceFormat &format) const;
    bool isValid() const;
    QVideoFrame::PixelFormat pixelFormat() const;
    QAbstractVideoBuffer::HandleType handleType() const;
    QSize frameSize() const;
    void setFrameSize(const QSize &size);
    void setFrameSize(int width, int height);
    int frameWidth() const;
    int frameHeight() const;
    QRect viewport() const;
    void setViewport(const QRect &viewport);
    QVideoSurfaceFormat::Direction scanLineDirection() const;
    void setScanLineDirection(QVideoSurfaceFormat::Direction direction);
    qreal frameRate() const;
    void setFrameRate(qreal rate);
    QSize pixelAspectRatio() const;
    void setPixelAspectRatio(const QSize &ratio);
    void setPixelAspectRatio(int width, int height);
    QVideoSurfaceFormat::YCbCrColorSpace yCbCrColorSpace() const;
    void setYCbCrColorSpace(QVideoSurfaceFormat::YCbCrColorSpace colorSpace);
    QSize sizeHint() const;
    QList<QByteArray> propertyNames() const;
    QVariant property(const char *name) const;
    void setProperty(const char *name, const QVariant &value);
%If (Qt_5_11_0 -)
    bool isMirrored() const;
%End
%If (Qt_5_11_0 -)
    void setMirrored(bool mirrored);
%End
};
