# -*- coding: utf-8 -*-
"""
LabelImg标注工具集成模块
集成LabelImg到昆虫检测项目中，提供便捷的数据标注功能
"""

import os
import sys
import subprocess
import json
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QFileDialog,
                             QMessageBox, QTextEdit, QGroupBox, QProgressBar,
                             QListWidget, QSplitter, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap
import Config

class LabelImgInstaller(QThread):
    """LabelImg安装线程"""
    progress_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(bool)
    
    def run(self):
        try:
            self.progress_signal.emit("正在检查LabelImg安装状态...")
            
            # 检查是否已安装
            try:
                import labelImg
                self.progress_signal.emit("✅ LabelImg已安装")
                self.finished_signal.emit(True)
                return
            except ImportError:
                pass
            
            # 尝试通过pip安装
            self.progress_signal.emit("正在安装LabelImg...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "labelImg"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                self.progress_signal.emit("✅ LabelImg安装成功")
                self.finished_signal.emit(True)
            else:
                self.progress_signal.emit(f"❌ 安装失败: {result.stderr}")
                self.finished_signal.emit(False)
                
        except Exception as e:
            self.progress_signal.emit(f"❌ 安装异常: {str(e)}")
            self.finished_signal.emit(False)

class DatasetManager:
    """数据集管理器"""
    
    def __init__(self):
        self.base_path = "datasets"
        self.images_path = os.path.join(self.base_path, "images")
        self.labels_path = os.path.join(self.base_path, "labels")
        
    def create_dataset_structure(self):
        """创建数据集目录结构"""
        dirs_to_create = [
            os.path.join(self.images_path, "train"),
            os.path.join(self.images_path, "val"),
            os.path.join(self.images_path, "test"),
            os.path.join(self.labels_path, "train"),
            os.path.join(self.labels_path, "val"),
            os.path.join(self.labels_path, "test"),
            "datasets/raw_images",  # 原始图片存放
            "datasets/predefined_classes"  # 预定义类别文件
        ]
        
        for dir_path in dirs_to_create:
            os.makedirs(dir_path, exist_ok=True)
    
    def create_predefined_classes_file(self):
        """创建预定义类别文件"""
        classes_file = "datasets/predefined_classes/classes.txt"
        
        with open(classes_file, 'w', encoding='utf-8') as f:
            for i, class_name in Config.names.items():
                f.write(f"{class_name}\n")
        
        return classes_file
    
    def get_image_stats(self):
        """获取数据集统计信息"""
        stats = {}
        
        for split in ['train', 'val', 'test']:
            img_dir = os.path.join(self.images_path, split)
            label_dir = os.path.join(self.labels_path, split)
            
            if os.path.exists(img_dir):
                images = [f for f in os.listdir(img_dir) 
                         if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
                stats[f'{split}_images'] = len(images)
            else:
                stats[f'{split}_images'] = 0
            
            if os.path.exists(label_dir):
                labels = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
                stats[f'{split}_labels'] = len(labels)
            else:
                stats[f'{split}_labels'] = 0
        
        return stats

class LabelingToolGUI(QMainWindow):
    """标注工具主界面"""
    
    def __init__(self):
        super().__init__()
        self.dataset_manager = DatasetManager()
        self.labelimg_process = None
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("昆虫检测数据标注工具")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧信息面板
        right_panel = self.create_info_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        # 应用样式
        self.apply_styles()
        
    def create_control_panel(self):
        """创建控制面板"""
        panel = QWidget()
        panel.setMaximumWidth(350)
        layout = QVBoxLayout(panel)
        
        # 环境设置组
        env_group = QGroupBox("环境设置")
        env_layout = QVBoxLayout(env_group)
        
        self.install_btn = QPushButton("安装/检查 LabelImg")
        self.install_btn.setMinimumHeight(40)
        env_layout.addWidget(self.install_btn)
        
        self.setup_btn = QPushButton("初始化数据集结构")
        self.setup_btn.setMinimumHeight(40)
        env_layout.addWidget(self.setup_btn)
        
        layout.addWidget(env_group)
        
        # 标注操作组
        label_group = QGroupBox("标注操作")
        label_layout = QVBoxLayout(label_group)
        
        self.import_btn = QPushButton("导入原始图片")
        self.import_btn.setMinimumHeight(35)
        label_layout.addWidget(self.import_btn)
        
        self.start_label_btn = QPushButton("启动 LabelImg 标注")
        self.start_label_btn.setMinimumHeight(35)
        label_layout.addWidget(self.start_label_btn)
        
        self.organize_btn = QPushButton("整理标注数据")
        self.organize_btn.setMinimumHeight(35)
        label_layout.addWidget(self.organize_btn)
        
        layout.addWidget(label_group)
        
        # 数据集管理组
        dataset_group = QGroupBox("数据集管理")
        dataset_layout = QVBoxLayout(dataset_group)
        
        self.split_btn = QPushButton("划分训练/验证/测试集")
        self.split_btn.setMinimumHeight(35)
        dataset_layout.addWidget(self.split_btn)
        
        self.validate_btn = QPushButton("验证数据集")
        self.validate_btn.setMinimumHeight(35)
        dataset_layout.addWidget(self.validate_btn)
        
        self.export_btn = QPushButton("导出YOLO格式")
        self.export_btn.setMinimumHeight(35)
        dataset_layout.addWidget(self.export_btn)
        
        layout.addWidget(dataset_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def create_info_panel(self):
        """创建信息面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 状态信息标签页
        status_tab = QWidget()
        status_layout = QVBoxLayout(status_tab)
        
        # 数据集统计
        stats_group = QGroupBox("数据集统计")
        stats_layout = QVBoxLayout(stats_group)
        
        self.stats_label = QLabel("点击'初始化数据集结构'开始")
        self.stats_label.setWordWrap(True)
        stats_layout.addWidget(self.stats_label)
        
        status_layout.addWidget(stats_group)
        
        # 操作日志
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        status_layout.addWidget(log_group)
        
        tab_widget.addTab(status_tab, "状态信息")
        
        # 帮助信息标签页
        help_tab = QWidget()
        help_layout = QVBoxLayout(help_tab)
        
        help_text = QTextEdit()
        help_text.setReadOnly(True)
        help_text.setHtml(self.get_help_content())
        help_layout.addWidget(help_text)
        
        tab_widget.addTab(help_tab, "使用帮助")
        
        return panel
    
    def get_help_content(self):
        """获取帮助内容"""
        return """
        <h3>昆虫检测数据标注工具使用指南</h3>
        
        <h4>1. 环境准备</h4>
        <ul>
            <li>点击"安装/检查 LabelImg"确保标注工具已安装</li>
            <li>点击"初始化数据集结构"创建必要的目录</li>
        </ul>
        
        <h4>2. 数据标注流程</h4>
        <ol>
            <li><b>导入图片</b>：点击"导入原始图片"选择要标注的图片</li>
            <li><b>开始标注</b>：点击"启动 LabelImg 标注"打开标注工具</li>
            <li><b>标注规范</b>：
                <ul>
                    <li>使用矩形框标注昆虫位置</li>
                    <li>选择正确的昆虫类别</li>
                    <li>确保标注框紧贴昆虫边缘</li>
                </ul>
            </li>
            <li><b>保存标注</b>：LabelImg会自动保存YOLO格式的标注文件</li>
        </ol>
        
        <h4>3. 数据集管理</h4>
        <ul>
            <li><b>整理数据</b>：将标注好的数据按比例分配到训练/验证/测试集</li>
            <li><b>验证数据</b>：检查标注文件的格式和完整性</li>
            <li><b>导出数据</b>：生成最终的YOLO训练格式</li>
        </ul>
        
        <h4>4. 支持的昆虫类别</h4>
        <ol start="0">
            <li>粘虫 (army worm)</li>
            <li>豆芫菁 (legume blister beetle)</li>
            <li>红蜘蛛 (red spider)</li>
            <li>稻瘿蚊 (rice gall midge)</li>
            <li>稻纵卷叶螟 (rice leaf roller)</li>
            <li>稻飞虱 (rice leafhopper)</li>
            <li>稻水象甲 (rice water weevil)</li>
            <li>麦长管蚜 (wheat phloeothrips)</li>
            <li>白背飞虱 (white backed plant hopper)</li>
            <li>黄稻螟 (yellow rice borer)</li>
        </ol>
        
        <h4>5. 注意事项</h4>
        <ul>
            <li>标注前确保图片清晰，昆虫特征明显</li>
            <li>每个昆虫都要用矩形框完整包围</li>
            <li>避免标注框过大或过小</li>
            <li>定期保存标注进度</li>
        </ul>
        """
    
    def setup_connections(self):
        """设置信号连接"""
        self.install_btn.clicked.connect(self.install_labelimg)
        self.setup_btn.clicked.connect(self.setup_dataset)
        self.import_btn.clicked.connect(self.import_images)
        self.start_label_btn.clicked.connect(self.start_labelimg)
        self.organize_btn.clicked.connect(self.organize_data)
        self.split_btn.clicked.connect(self.split_dataset)
        self.validate_btn.clicked.connect(self.validate_dataset)
        self.export_btn.clicked.connect(self.export_dataset)
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 8px;
                margin-top: 1ex;
                padding-top: 10px;
                background-color: #ffffff;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                color: #2c3e50;
            }
            
            QPushButton {
                background-color: #3498db;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 6px;
                font-weight: bold;
            }
            
            QPushButton:hover {
                background-color: #2980b9;
            }
            
            QPushButton:pressed {
                background-color: #21618c;
            }
            
            QTextEdit {
                border: 1px solid #cccccc;
                border-radius: 4px;
                background-color: #ffffff;
            }
            
            QLabel {
                color: #2c3e50;
                font-size: 13px;
            }
        """)
    
    def log_message(self, message):
        """记录日志消息"""
        self.log_text.append(f"[{QTimer().remainingTime()}] {message}")
        QApplication.processEvents()
    
    def install_labelimg(self):
        """安装LabelImg"""
        self.log_message("开始安装LabelImg...")
        self.install_btn.setEnabled(False)
        
        # 创建安装线程
        self.installer = LabelImgInstaller()
        self.installer.progress_signal.connect(self.log_message)
        self.installer.finished_signal.connect(self.on_install_finished)
        self.installer.start()
    
    def on_install_finished(self, success):
        """安装完成回调"""
        self.install_btn.setEnabled(True)
        if success:
            self.log_message("✅ LabelImg安装完成，可以开始标注")
        else:
            self.log_message("❌ LabelImg安装失败，请手动安装")
    
    def setup_dataset(self):
        """初始化数据集结构"""
        try:
            self.log_message("正在创建数据集目录结构...")
            self.dataset_manager.create_dataset_structure()
            
            self.log_message("正在创建预定义类别文件...")
            classes_file = self.dataset_manager.create_predefined_classes_file()
            
            self.log_message(f"✅ 数据集结构初始化完成")
            self.log_message(f"类别文件: {classes_file}")
            
            self.update_stats()
            
        except Exception as e:
            self.log_message(f"❌ 初始化失败: {str(e)}")
    
    def update_stats(self):
        """更新统计信息"""
        stats = self.dataset_manager.get_image_stats()
        
        stats_text = f"""
        <b>数据集统计信息：</b><br>
        训练集：{stats['train_images']} 张图片，{stats['train_labels']} 个标签<br>
        验证集：{stats['val_images']} 张图片，{stats['val_labels']} 个标签<br>
        测试集：{stats['test_images']} 张图片，{stats['test_labels']} 个标签<br>
        <br>
        <b>总计：</b>{stats['train_images'] + stats['val_images'] + stats['test_images']} 张图片
        """
        
        self.stats_label.setText(stats_text)
    
    def import_images(self):
        """导入原始图片"""
        folder = QFileDialog.getExistingDirectory(self, "选择图片文件夹", "./")
        if not folder:
            return
        
        try:
            import shutil
            
            raw_images_dir = "datasets/raw_images"
            supported_formats = ['.jpg', '.jpeg', '.png', '.bmp']
            
            copied_count = 0
            for filename in os.listdir(folder):
                if any(filename.lower().endswith(ext) for ext in supported_formats):
                    src = os.path.join(folder, filename)
                    dst = os.path.join(raw_images_dir, filename)
                    shutil.copy2(src, dst)
                    copied_count += 1
            
            self.log_message(f"✅ 成功导入 {copied_count} 张图片到 {raw_images_dir}")
            
        except Exception as e:
            self.log_message(f"❌ 导入图片失败: {str(e)}")
    
    def start_labelimg(self):
        """启动LabelImg标注工具"""
        try:
            # 检查原始图片目录
            raw_images_dir = "datasets/raw_images"
            if not os.path.exists(raw_images_dir) or not os.listdir(raw_images_dir):
                QMessageBox.warning(self, "警告", "请先导入原始图片！")
                return
            
            # 检查类别文件
            classes_file = "datasets/predefined_classes/classes.txt"
            if not os.path.exists(classes_file):
                QMessageBox.warning(self, "警告", "请先初始化数据集结构！")
                return
            
            # 启动LabelImg
            self.log_message("正在启动LabelImg...")
            
            # 构建启动命令 - 直接使用labelImg命令
            cmd = [
                "labelImg",
                raw_images_dir,  # 图片目录
                classes_file     # 类别文件
            ]
            
            # 启动进程
            self.labelimg_process = subprocess.Popen(cmd)
            self.log_message("✅ LabelImg已启动，请在新窗口中进行标注")
            
        except Exception as e:
            self.log_message(f"❌ 启动LabelImg失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"启动失败：{str(e)}")
    
    def organize_data(self):
        """整理标注数据"""
        self.log_message("数据整理功能开发中...")
        QMessageBox.information(self, "提示", "此功能正在开发中，请手动整理标注数据")
    
    def split_dataset(self):
        """划分数据集"""
        self.log_message("数据集划分功能开发中...")
        QMessageBox.information(self, "提示", "此功能正在开发中")
    
    def validate_dataset(self):
        """验证数据集"""
        self.log_message("正在验证数据集...")
        self.update_stats()
        self.log_message("✅ 数据集验证完成")
    
    def export_dataset(self):
        """导出数据集"""
        self.log_message("数据集导出功能开发中...")
        QMessageBox.information(self, "提示", "此功能正在开发中")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("昆虫检测数据标注工具")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    window = LabelingToolGUI()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
