# -*- coding: utf-8 -*-
"""
标注工具诊断脚本
帮助用户排查标注工具的常见问题
"""

import os
import sys
import subprocess
import importlib

def check_python_environment():
    """检查Python环境"""
    print("=" * 50)
    print("检查Python环境")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 检查Python版本
    version_info = sys.version_info
    if version_info.major < 3 or (version_info.major == 3 and version_info.minor < 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_required_packages():
    """检查必要的包"""
    print("\n" + "=" * 50)
    print("检查必要的包")
    print("=" * 50)
    
    required_packages = {
        'PyQt5': 'PyQt5',
        'labelImg': 'labelImg',
        'cv2': 'opencv-python',
        'numpy': 'numpy',
        'PIL': 'Pillow'
    }
    
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            importlib.import_module(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装 (pip install {pip_name})")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n⚠️  缺少以下包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_labelimg_functionality():
    """检查LabelImg功能"""
    print("\n" + "=" * 50)
    print("检查LabelImg功能")
    print("=" * 50)
    
    try:
        # 尝试导入LabelImg
        import labelImg
        print("✅ LabelImg模块导入成功")
        
        # 检查是否可以通过命令行启动
        result = subprocess.run([
            sys.executable, "-c", "import labelImg; print('LabelImg import test passed')"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ LabelImg命令行测试通过")
            return True
        else:
            print(f"❌ LabelImg命令行测试失败: {result.stderr}")
            return False
            
    except ImportError:
        print("❌ LabelImg未安装")
        return False
    except subprocess.TimeoutExpired:
        print("❌ LabelImg启动超时")
        return False
    except Exception as e:
        print(f"❌ LabelImg测试失败: {e}")
        return False

def check_directory_structure():
    """检查目录结构"""
    print("\n" + "=" * 50)
    print("检查目录结构")
    print("=" * 50)
    
    required_dirs = [
        "datasets",
        "datasets/raw_images",
        "datasets/images",
        "datasets/labels",
        "datasets/predefined_classes"
    ]
    
    all_exist = True
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"✅ {dir_path}")
        else:
            print(f"❌ {dir_path} - 不存在")
            all_exist = False
    
    return all_exist

def check_script_files():
    """检查脚本文件"""
    print("\n" + "=" * 50)
    print("检查脚本文件")
    print("=" * 50)
    
    required_files = [
        "start_labeling.py",
        "labeling_tool.py",
        "organize_dataset.py",
        "check_annotations.py",
        "Config.py"
    ]
    
    all_exist = True
    
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size} bytes)")
        else:
            print(f"❌ {file_path} - 不存在")
            all_exist = False
    
    return all_exist

def check_classes_file():
    """检查类别文件"""
    print("\n" + "=" * 50)
    print("检查类别文件")
    print("=" * 50)
    
    classes_file = "datasets/predefined_classes/classes.txt"
    
    if not os.path.exists(classes_file):
        print(f"❌ 类别文件不存在: {classes_file}")
        
        # 尝试创建
        try:
            os.makedirs(os.path.dirname(classes_file), exist_ok=True)
            
            import Config
            with open(classes_file, 'w', encoding='utf-8') as f:
                for i in range(len(Config.names)):
                    f.write(f"{Config.names[i]}\n")
            
            print(f"✅ 已自动创建类别文件: {classes_file}")
            return True
            
        except Exception as e:
            print(f"❌ 创建类别文件失败: {e}")
            return False
    else:
        try:
            with open(classes_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            class_count = len([line for line in lines if line.strip()])
            print(f"✅ 类别文件存在，包含 {class_count} 个类别")
            
            if class_count != 10:
                print(f"⚠️  类别数量不正确，应为10个")
            
            return True
            
        except Exception as e:
            print(f"❌ 读取类别文件失败: {e}")
            return False

def check_batch_file():
    """检查批处理文件"""
    print("\n" + "=" * 50)
    print("检查批处理文件")
    print("=" * 50)
    
    batch_files = ["start_labeling.bat", "start_labeling_simple.bat"]
    
    working_files = []
    
    for batch_file in batch_files:
        if os.path.exists(batch_file):
            size = os.path.getsize(batch_file)
            print(f"✅ {batch_file} ({size} bytes)")
            working_files.append(batch_file)
        else:
            print(f"❌ {batch_file} - 不存在")
    
    if working_files:
        print(f"\n💡 推荐使用: {working_files[0]}")
        return True
    else:
        print("\n❌ 没有可用的批处理文件")
        return False

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 50)
    print("常见问题解决方案")
    print("=" * 50)
    
    print("\n1. 如果LabelImg未安装:")
    print("   pip install labelImg")
    
    print("\n2. 如果PyQt5有问题:")
    print("   pip uninstall PyQt5")
    print("   pip install PyQt5==5.15.7")
    
    print("\n3. 如果批处理文件闪退:")
    print("   - 使用 start_labeling_simple.bat")
    print("   - 或直接运行: python start_labeling.py")
    
    print("\n4. 如果目录不存在:")
    print("   python start_labeling.py  # 会自动创建目录")
    
    print("\n5. 如果编码问题:")
    print("   - 确保系统支持UTF-8编码")
    print("   - 使用PowerShell而不是CMD")
    
    print("\n6. 如果权限问题:")
    print("   - 以管理员身份运行")
    print("   - 检查文件夹权限")

def run_quick_fix():
    """运行快速修复"""
    print("\n" + "=" * 50)
    print("运行快速修复")
    print("=" * 50)
    
    try:
        # 创建目录结构
        dirs = [
            "datasets/raw_images",
            "datasets/images/train",
            "datasets/images/val", 
            "datasets/images/test",
            "datasets/labels/train",
            "datasets/labels/val",
            "datasets/labels/test",
            "datasets/predefined_classes"
        ]
        
        for dir_path in dirs:
            os.makedirs(dir_path, exist_ok=True)
        
        print("✅ 目录结构修复完成")
        
        # 创建类别文件
        classes_file = "datasets/predefined_classes/classes.txt"
        if not os.path.exists(classes_file):
            import Config
            with open(classes_file, 'w', encoding='utf-8') as f:
                for i in range(len(Config.names)):
                    f.write(f"{Config.names[i]}\n")
            print("✅ 类别文件创建完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速修复失败: {e}")
        return False

def main():
    """主函数"""
    print("昆虫检测标注工具诊断程序")
    print("=" * 50)
    
    # 运行所有检查
    checks = [
        ("Python环境", check_python_environment),
        ("必要包", check_required_packages),
        ("LabelImg功能", check_labelimg_functionality),
        ("目录结构", check_directory_structure),
        ("脚本文件", check_script_files),
        ("类别文件", check_classes_file),
        ("批处理文件", check_batch_file),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
            else:
                print(f"⚠️  {check_name} 检查失败")
        except Exception as e:
            print(f"❌ {check_name} 检查异常: {e}")
    
    # 显示结果
    print("\n" + "=" * 50)
    print(f"诊断完成: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！标注工具应该可以正常使用")
        print("💡 运行 start_labeling_simple.bat 开始标注")
    elif passed >= total * 0.8:
        print("⚠️  大部分检查通过，可能存在小问题")
        
        # 询问是否运行快速修复
        response = input("\n是否运行快速修复? (y/n): ").lower()
        if response == 'y':
            if run_quick_fix():
                print("✅ 快速修复完成，请重新运行诊断")
            else:
                provide_solutions()
        else:
            provide_solutions()
    else:
        print("❌ 多项检查失败，需要手动修复")
        provide_solutions()
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
