// qcompleter.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QCompleter : QObject
{
%TypeHeaderCode
#include <qcompleter.h>
%End

public:
    enum CompletionMode
    {
        PopupCompletion,
        UnfilteredPopupCompletion,
        InlineCompletion,
    };

    enum ModelSorting
    {
        UnsortedModel,
        CaseSensitivelySortedModel,
        CaseInsensitivelySortedModel,
    };

    QCompleter(QAbstractItemModel *model, QObject *parent /TransferThis/ = 0);
    QCompleter(const QStringList &list, QObject *parent /TransferThis/ = 0);
    QCompleter(QObject *parent /TransferThis/ = 0);
    virtual ~QCompleter();
    void setWidget(QWidget *widget /Transfer/);
    QWidget *widget() const;
    void setModel(QAbstractItemModel *c /KeepReference/);
    QAbstractItemModel *model() const;
    void setCompletionMode(QCompleter::CompletionMode mode);
    QCompleter::CompletionMode completionMode() const;
    QAbstractItemView *popup() const;
    void setPopup(QAbstractItemView *popup /Transfer/);
    void setCaseSensitivity(Qt::CaseSensitivity caseSensitivity);
    Qt::CaseSensitivity caseSensitivity() const;
    void setModelSorting(QCompleter::ModelSorting sorting);
    QCompleter::ModelSorting modelSorting() const;
    void setCompletionColumn(int column);
    int completionColumn() const;
    void setCompletionRole(int role);
    int completionRole() const;
    int completionCount() const;
    bool setCurrentRow(int row);
    int currentRow() const;
    QModelIndex currentIndex() const;
    QString currentCompletion() const;
    QAbstractItemModel *completionModel() const;
    QString completionPrefix() const;
    virtual QString pathFromIndex(const QModelIndex &index) const;
    virtual QStringList splitPath(const QString &path) const;
    bool wrapAround() const;

public slots:
    void complete(const QRect &rect = QRect());
    void setCompletionPrefix(const QString &prefix);
    void setWrapAround(bool wrap);

protected:
    virtual bool eventFilter(QObject *o, QEvent *e);
    virtual bool event(QEvent *);

signals:
    void activated(const QString &text);
    void activated(const QModelIndex &index);
    void highlighted(const QString &text);
    void highlighted(const QModelIndex &index);

public:
    int maxVisibleItems() const;
    void setMaxVisibleItems(int maxItems);
%If (Qt_5_2_0 -)
    void setFilterMode(Qt::MatchFlags filterMode);
%End
%If (Qt_5_2_0 -)
    Qt::MatchFlags filterMode() const;
%End
};
