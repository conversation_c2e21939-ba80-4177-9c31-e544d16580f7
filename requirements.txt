# YOLOv8昆虫检测系统依赖包

# 深度学习框架
torch>=1.9.0
torchvision>=0.10.0
ultralytics>=8.0.0

# 计算机视觉
opencv-python>=4.5.0
opencv-contrib-python>=4.5.0
Pillow>=8.0.0

# GUI框架
PyQt5>=5.15.0

# 数据标注工具
labelImg>=1.8.6

# 数据处理
numpy>=1.21.0
pandas>=1.3.0

# 图像处理
matplotlib>=3.3.0
seaborn>=0.11.0

# 系统工具
psutil>=5.8.0
tqdm>=4.62.0

# 其他工具
requests>=2.25.0
pyyaml>=5.4.0
pathlib2>=2.3.0

# 可选：GPU支持（如果有NVIDIA GPU）
# torch-audio>=0.9.0
# torchaudio>=0.9.0

# 开发工具（可选）
# pytest>=6.0.0
# black>=21.0.0
# flake8>=3.9.0
