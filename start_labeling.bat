@echo off
chcp 65001 >nul
title 昆虫检测数据标注工具

echo ========================================
echo    昆虫检测数据标注工具
echo ========================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo [信息] Python环境检查通过
echo.

:: 显示菜单
:menu
echo ========================================
echo 请选择操作:
echo ========================================
echo 1. 快速启动标注工具 (推荐)
echo 2. 启动完整标注界面
echo 3. 整理标注数据
echo 4. 验证数据集
echo 5. 查看帮助
echo 6. 退出
echo ========================================
set /p choice="请输入选项 (1-6): "

if "%choice%"=="1" goto quick_start
if "%choice%"=="2" goto full_gui
if "%choice%"=="3" goto organize
if "%choice%"=="4" goto validate
if "%choice%"=="5" goto help
if "%choice%"=="6" goto exit
echo [错误] 无效选项，请重新选择
goto menu

:quick_start
echo.
echo [信息] 启动快速标注工具...
python start_labeling.py
pause
goto menu

:full_gui
echo.
echo [信息] 启动完整标注界面...
python labeling_tool.py
pause
goto menu

:organize
echo.
echo [信息] 整理标注数据...
python organize_dataset.py
pause
goto menu

:validate
echo.
echo [信息] 验证数据集...
python validate_config.py
pause
goto menu

:help
echo.
echo ========================================
echo 昆虫检测数据标注工具使用帮助
echo ========================================
echo.
echo 📋 标注流程:
echo 1. 将要标注的图片放入 datasets/raw_images/ 目录
echo 2. 选择 "快速启动标注工具" 或 "启动完整标注界面"
echo 3. 在LabelImg中进行标注
echo 4. 标注完成后选择 "整理标注数据"
echo 5. 使用整理好的数据进行模型训练
echo.
echo 🐛 支持的昆虫类别:
echo 0: 粘虫 (army worm)
echo 1: 豆芫菁 (legume blister beetle)
echo 2: 红蜘蛛 (red spider)
echo 3: 稻瘿蚊 (rice gall midge)
echo 4: 稻纵卷叶螟 (rice leaf roller)
echo 5: 稻飞虱 (rice leafhopper)
echo 6: 稻水象甲 (rice water weevil)
echo 7: 麦长管蚜 (wheat phloeothrips)
echo 8: 白背飞虱 (white backed plant hopper)
echo 9: 黄稻螟 (yellow rice borer)
echo.
echo 📁 目录结构:
echo datasets/
echo ├── raw_images/          # 原始图片(待标注)
echo ├── images/
echo │   ├── train/          # 训练集图片
echo │   ├── val/            # 验证集图片
echo │   └── test/           # 测试集图片
echo ├── labels/
echo │   ├── train/          # 训练集标签
echo │   ├── val/            # 验证集标签
echo │   └── test/           # 测试集标签
echo └── predefined_classes/
echo     └── classes.txt     # 类别定义文件
echo.
echo 💡 标注技巧:
echo - 使用矩形框紧贴昆虫边缘
echo - 确保选择正确的昆虫类别
echo - 每个昆虫都要单独标注
echo - 定期保存标注进度 (Ctrl+S)
echo.
pause
goto menu

:exit
echo.
echo [信息] 感谢使用昆虫检测数据标注工具!
echo.
exit /b 0
