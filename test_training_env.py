# -*- coding: utf-8 -*-
"""
训练环境测试脚本
Test training environment for YOLOv8 insect detection
"""

import os
import sys
import yaml

def test_python_environment():
    """测试Python环境"""
    print("=" * 50)
    print("Python环境测试")
    print(f"Python版本: {sys.version}")
    
    # 测试必要的库
    required_packages = [
        'torch',
        'ultralytics', 
        'yaml',
        'PIL',
        'cv2'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install ultralytics torch pillow opencv-python pyyaml")
        return False
    
    return True

def test_cuda_availability():
    """测试CUDA可用性"""
    print("\n" + "=" * 50)
    print("CUDA环境测试")
    
    try:
        import torch
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            device_name = torch.cuda.get_device_name(current_device)
            memory_total = torch.cuda.get_device_properties(current_device).total_memory / 1024**3
            
            print(f"✅ CUDA可用")
            print(f"🎯 设备数量: {device_count}")
            print(f"🎯 当前设备: {device_name}")
            print(f"🎯 显存大小: {memory_total:.1f} GB")
            return 'cuda'
        else:
            print("⚠️  CUDA不可用，将使用CPU训练")
            return 'cpu'
    except Exception as e:
        print(f"❌ CUDA测试失败: {e}")
        return 'cpu'

def test_dataset_config():
    """测试数据集配置"""
    print("\n" + "=" * 50)
    print("数据集配置测试")
    
    config_path = 'datasets/data.yaml'
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ 配置文件加载成功")
        
        # 检查必要字段
        required_fields = ['train', 'val', 'nc', 'names']
        for field in required_fields:
            if field in config:
                print(f"✅ {field}: {config[field] if field != 'names' else f'{len(config[field])} 个类别'}")
            else:
                print(f"❌ 缺少字段: {field}")
                return False
        
        # 检查目录是否存在
        for split in ['train', 'val']:
            if split in config:
                path = config[split]
                if os.path.exists(path):
                    file_count = len([f for f in os.listdir(path) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
                    print(f"✅ {split}目录存在: {path} ({file_count} 张图片)")
                else:
                    print(f"⚠️  {split}目录不存在: {path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件解析失败: {e}")
        return False

def test_yolo_model():
    """测试YOLO模型加载"""
    print("\n" + "=" * 50)
    print("YOLO模型测试")
    
    try:
        from ultralytics import YOLO
        
        # 测试加载预训练模型
        print("📥 加载YOLOv8s预训练模型...")
        model = YOLO('yolov8s.pt')
        print("✅ 模型加载成功")
        
        # 测试模型信息
        print(f"✅ 模型类型: {type(model)}")
        print(f"✅ 模型任务: {model.task}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return False

def test_training_script():
    """测试训练脚本"""
    print("\n" + "=" * 50)
    print("训练脚本测试")
    
    if not os.path.exists('train.py'):
        print("❌ 训练脚本不存在: train.py")
        return False
    
    print("✅ 训练脚本存在")
    
    # 检查脚本语法
    try:
        with open('train.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'train.py', 'exec')
        print("✅ 训练脚本语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ 训练脚本语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 训练脚本检查失败: {e}")
        return False

def generate_sample_data():
    """生成示例数据用于测试"""
    print("\n" + "=" * 50)
    print("生成示例数据")
    
    try:
        import numpy as np
        from PIL import Image
        
        # 创建示例图片和标签
        for split in ['train', 'val']:
            img_dir = f'datasets/images/{split}'
            label_dir = f'datasets/labels/{split}'
            
            # 创建几张示例图片
            for i in range(3):
                # 创建随机图片
                img_array = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
                img = Image.fromarray(img_array)
                img_path = os.path.join(img_dir, f'sample_{i}.jpg')
                img.save(img_path)
                
                # 创建对应的标签文件
                label_path = os.path.join(label_dir, f'sample_{i}.txt')
                with open(label_path, 'w') as f:
                    # 写入一个示例标注（类别0，中心位置0.5,0.5，尺寸0.2,0.2）
                    f.write('0 0.5 0.5 0.2 0.2\n')
            
            print(f"✅ 生成 {split} 示例数据: 3张图片和标签")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成示例数据失败: {e}")
        return False

def main():
    """主测试函数"""
    print("YOLOv8昆虫检测训练环境测试")
    print("=" * 50)
    
    tests = [
        ("Python环境", test_python_environment),
        ("CUDA环境", test_cuda_availability),
        ("数据集配置", test_dataset_config),
        ("YOLO模型", test_yolo_model),
        ("训练脚本", test_training_script),
    ]
    
    passed = 0
    total = len(tests)
    device = 'cpu'
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if test_name == "CUDA环境":
                device = result if isinstance(result, str) else 'cpu'
                result = True  # CUDA测试总是通过，只是确定设备类型
            
            if result:
                passed += 1
            else:
                print(f"\n❌ 测试失败: {test_name}")
        except Exception as e:
            print(f"\n❌ 测试异常: {test_name} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！训练环境就绪")
        print(f"🎯 推荐训练设备: {device}")
        
        # 检查是否有数据
        train_dir = 'datasets/images/train'
        if os.path.exists(train_dir):
            img_count = len([f for f in os.listdir(train_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
            if img_count == 0:
                print("\n⚠️  训练目录为空，是否生成示例数据进行测试？")
                response = input("输入 'y' 生成示例数据: ").lower()
                if response == 'y':
                    generate_sample_data()
        
        print("\n🚀 可以开始训练:")
        print("   方法1: 运行 start_training.bat")
        print("   方法2: 运行 python train.py")
        
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，可以尝试训练")
        print("🔧 建议修复失败的测试项")
    else:
        print("❌ 多项测试失败，请修复环境问题")
    
    print("=" * 50)
    return passed == total

if __name__ == "__main__":
    success = main()
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
