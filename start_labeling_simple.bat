@echo off
title Insect Detection Labeling Tool

echo ========================================
echo    Insect Detection Labeling Tool
echo ========================================
echo.

REM Check Python environment
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python not found, please install Python 3.7+
    pause
    exit /b 1
)

echo [INFO] Python environment check passed
echo.

REM Show menu
:menu
echo ========================================
echo Please select an option:
echo ========================================
echo 1. Quick start labeling tool
echo 2. Launch full labeling GUI
echo 3. Organize labeled data
echo 4. Check annotation quality
echo 5. Exit
echo ========================================
set /p choice="Please enter option (1-5): "

if "%choice%"=="1" goto quick_start
if "%choice%"=="2" goto full_gui
if "%choice%"=="3" goto organize
if "%choice%"=="4" goto validate
if "%choice%"=="5" goto exit
echo [ERROR] Invalid option, please try again
goto menu

:quick_start
echo.
echo [INFO] Starting quick labeling tool...
python start_labeling.py
pause
goto menu

:full_gui
echo.
echo [INFO] Starting full labeling GUI...
python labeling_tool.py
pause
goto menu

:organize
echo.
echo [INFO] Organizing labeled data...
python organize_dataset.py
pause
goto menu

:validate
echo.
echo [INFO] Checking annotation quality...
python check_annotations.py
pause
goto menu

:exit
echo.
echo [INFO] Thank you for using Insect Detection Labeling Tool!
echo.
pause
exit /b 0
