
# parsetab.py
# This file is automatically generated. Do not edit.
# pylint: disable=W,C,R
_tabversion = '3.10'

_lr_method = 'LALR'

_lr_signature = "specificationleftSCOPEAccessCode AutoPyName BIGetBufferCode BIGetCharBufferCode BIGetReadBufferCode BIGetSegCountCode BIGetWriteBufferCode BIReleaseBufferCode CODE_BLOCK CompositeModule ConvertFromTypeCode ConvertToSubClassCode ConvertToTypeCode Copying DOTTED_NAME DefaultDocstringFormat DefaultDocstringSignature DefaultEncoding DefaultMetatype DefaultSupertype Docstring ELLIPSIS EOF EOL End Exception ExportedHeaderCode ExportedTypeHintCode Extract FILE_PATH False Feature FinalisationCode GCClearCode GCTraverseCode GetCode HideNamespace If Import Include InitialisationCode InstanceCode LOGICAL_OR License MappedType MethodCode Module ModuleCode ModuleHeaderCode NAME NULL NUMBER PickleCode Platforms Plugin PostInitialisationCode PreInitialisationCode PreMethodCode Property Py_hash_t Py_ssize_t QUOTED_CHAR Q_SIGNAL Q_SIGNALS Q_SLOT Q_SLOTS REAL RaiseCode ReleaseCode SCOPE SIP_PYBUFFER SIP_PYCALLABLE SIP_PYDICT SIP_PYENUM SIP_PYLIST SIP_PYOBJECT SIP_PYSLICE SIP_PYTUPLE SIP_PYTYPE SIP_SSIZE_T STRING SetCode Timeline True TypeCode TypeHeaderCode TypeHintCode UnitCode UnitPostIncludeCode VirtualCallCode VirtualCatcherCode VirtualErrorHandler all_raise_py_exception bool call_super_init char class const default_VirtualErrorHandler double enum explicit false final float format get id int keyword_arguments language licensee long name namespace noexcept operator optional order private protected public py_ssize_t_clean remove_leading set short signals signature signed size_t slots static struct template throw timestamp true type typedef union unsigned use_argument_names use_limited_api virtual void wchar_tspecification : statement\n        | specification statementstatement : eof\n        | namespace_statement\n        | composite_module\n        | copying\n        | defdocstringfmt\n        | defdocstringsig\n        | defencoding\n        | defmetatype\n        | defsupertype\n        | exported_header_code\n        | exported_type_hint_code\n        | extract\n        | feature\n        | hidden_ns\n        | import\n        | include\n        | init_code\n        | license\n        | mapped_type\n        | mapped_type_template\n        | module\n        | module_code\n        | module_header_code\n        | platforms\n        | plugin\n        | preinit_code\n        | postinit_code\n        | timeline\n        | type_hint_code\n        | unit_code\n        | unit_postinclude_code\n        | virtual_error_handlernamespace_statement : if_start\n        | if_end\n        | class_decl\n        | class_template\n        | enum_decl\n        | exception\n        | function\n        | namespace_decl\n        | struct_decl\n        | typedef_decl\n        | union_decl\n        | variable\n        | type_header_codeeof : EOFbegin_args :end_args :need_eol :autopyname : AutoPyName begin_args '(' remove_leading '=' STRING end_args ')'get_buffer_code : BIGetBufferCode CODE_BLOCKrelease_buffer_code : BIReleaseBufferCode CODE_BLOCKcomposite_module : CompositeModule dotted_name c_module_body\n        | CompositeModule begin_args '(' c_module_args end_args ')' c_module_bodyc_module_args : c_module_arg\n        | c_module_args ',' c_module_argc_module_arg : name '=' dotted_namec_module_body : '{' c_module_body_directives '}' ';'\n        | emptyc_module_body_directives : c_module_body_directive\n        | c_module_body_directives c_module_body_directivec_module_body_directive : if_start\n        | if_end\n        | docstringconvert_from_type_code : ConvertFromTypeCode CODE_BLOCKconvert_to_subclass_code : ConvertToSubClassCode CODE_BLOCKconvert_to_type_code : ConvertToTypeCode CODE_BLOCKcopying : Copying CODE_BLOCKdefdocstringfmt : DefaultDocstringFormat STRING\n        | DefaultDocstringFormat begin_args '(' name '=' STRING end_args ')'defdocstringsig : DefaultDocstringSignature STRING\n        | DefaultDocstringSignature begin_args '(' name '=' STRING end_args ')'defencoding : DefaultEncoding STRING\n        |  DefaultEncoding begin_args '(' name '=' STRING end_args ')'defmetatype : DefaultMetatype dotted_name\n        | DefaultMetatype begin_args '(' name '=' dotted_name end_args ')'defsupertype : DefaultSupertype dotted_name\n        | DefaultSupertype begin_args '(' name '=' dotted_name end_args ')'docstring : Docstring docstring_args CODE_BLOCKdocstring_args : empty\n    | STRING\n    | begin_args '(' docstring_arg_list end_args ')'docstring_arg_list : docstring_arg\n        | docstring_arg_list ',' docstring_argdocstring_arg : format '=' STRING\n    | signature '=' STRINGexported_header_code : ExportedHeaderCode CODE_BLOCKexported_type_hint_code : ExportedTypeHintCode CODE_BLOCKextract : Extract NAME CODE_BLOCK\n        | Extract begin_args '(' extract_args end_args ')' CODE_BLOCKextract_args : extract_arg\n        | extract_args ',' extract_argextract_arg : id '=' NAME\n        | order '=' NUMBERfeature : Feature NAME\n        | Feature begin_args '(' name '=' NAME end_args ')'finalisation_code : FinalisationCode CODE_BLOCKgc_clear_code : GCClearCode CODE_BLOCKgc_traverse_code : GCTraverseCode CODE_BLOCKhidden_ns : HideNamespace scoped_name\n        | HideNamespace begin_args '(' hidden_ns_args end_args ')'hidden_ns_args : hidden_ns_arg\n        | hidden_ns_args ',' hidden_ns_arghidden_ns_arg : name '=' scoped_nameif_start : If '(' qualifiers ')'if_end : Endimport : Import need_eol import_simple EOL\n        | Import begin_args '(' import_compound end_args ')'import_simple : file_pathimport_compound : import_argsimport_args : import_arg\n        | import_args ',' import_argimport_arg : name '=' file_pathinclude : Include need_eol include_simple EOL\n        | Include begin_args '(' include_compound end_args ')'include_simple : file_pathinclude_compound : include_argsinclude_args : include_arg\n        | include_args ',' include_arginclude_arg : name '=' file_path\n        | optional '=' bool_valueinit_code : InitialisationCode CODE_BLOCKinstance_code : InstanceCode CODE_BLOCKlicense : License STRING\n        | License begin_args '(' license_args end_args ')'license_args : license_arg\n        | license_args ',' license_arglicense_arg : licensee '=' STRING\n        | signature '=' STRING\n        | timestamp '=' STRING\n        | type '=' STRINGmapped_type : mapped_type_head '{' mapped_type_body '}' ';'mapped_type_template : mapped_type_template_head '{' mapped_type_body '}' ';'mapped_type_head : MappedType base_type opt_annosmapped_type_template_head : template_decl MappedType base_type opt_annosmapped_type_body : mapped_type_line\n        | mapped_type_body mapped_type_linemapped_type_line : if_start\n        | if_end\n        | convert_from_type_code\n        | convert_to_type_code\n        | enum_decl\n        | instance_code\n        | mapped_type_function\n        | release_code\n        | type_code\n        | type_header_codemapped_type_function : static cpp_type NAME '(' opt_arg_list ')' opt_const opt_exceptions opt_annos opt_signature ';' opt_docstring premethod_code method_codemodule_header_code : ModuleHeaderCode CODE_BLOCKmodule : Module dotted_name module_body\n        | Module begin_args '(' module_args end_args ')' module_bodymodule_args : module_arg\n        | module_args ',' module_argmodule_arg : all_raise_py_exception '=' bool_value\n        | call_super_init '=' bool_value\n        | default_VirtualErrorHandler '=' NAME\n        | keyword_arguments '=' STRING\n        | language '=' STRING\n        | name '=' dotted_name\n        | py_ssize_t_clean '=' bool_value\n        | use_argument_names '=' bool_value\n        | use_limited_api '=' bool_valuemodule_body : '{' module_body_directives '}' ';'\n        | emptymodule_body_directives : module_body_directive\n        | module_body_directives module_body_directivemodule_body_directive : if_start\n        | if_end\n        | autopyname\n        | docstringmodule_code : ModuleCode CODE_BLOCKpickle_code : PickleCode CODE_BLOCKplatforms : Platforms '{' qualifier_list '}'plugin : Plugin NAMEpostinit_code : PostInitialisationCode CODE_BLOCKpreinit_code : PreInitialisationCode CODE_BLOCKproperty : Property begin_args '(' property_args end_args ')' opt_property_bodyproperty_args : property_arg\n        | property_args ',' property_argproperty_arg : get '=' NAME\n        | name '=' NAME\n        | set '=' NAMEopt_property_body : empty\n        | '{' property_body '}' ';'property_body : property_line\n        | property_body property_lineproperty_line : if_start\n        | if_end\n        | docstringrelease_code : ReleaseCode CODE_BLOCKtimeline : Timeline '{' qualifier_list '}'type_code : TypeCode CODE_BLOCKtype_header_code : TypeHeaderCode CODE_BLOCKtype_hint_code : TypeHintCode CODE_BLOCKunit_code : UnitCode CODE_BLOCKunit_postinclude_code : UnitPostIncludeCode CODE_BLOCKvirtual_error_handler : VirtualErrorHandler NAME CODE_BLOCK\n        | VirtualErrorHandler begin_args '(' veh_args end_args ')' CODE_BLOCKveh_args : veh_arg\n        | veh_args ',' veh_argveh_arg : name '=' NAMEcpp_type : const base_type derefs opt_ref\n        | base_type derefs opt_refbase_type : pod_type\n        | scoped_name\n        | scoped_name '<' cpp_types '>'\n        | struct scoped_name\n        | union scoped_namepod_type : unsigned long long\n        | signed char\n        | long long\n        | unsigned char\n        | unsigned short\n        | unsigned int\n        | unsigned long\n        | unsigned\n        | short\n        | int\n        | long\n        | float\n        | double\n        | bool\n        | char\n        | wchar_t\n        | void\n        | SIP_PYOBJECT\n        | SIP_PYTUPLE\n        | SIP_PYLIST\n        | SIP_PYDICT\n        | SIP_PYCALLABLE\n        | SIP_PYSLICE\n        | SIP_PYTYPE\n        | SIP_PYBUFFER\n        | SIP_PYENUM\n        | SIP_SSIZE_T\n        | Py_hash_t\n        | Py_ssize_t\n        | size_t\n        | ELLIPSIScpp_types : cpp_type\n        | cpp_types ',' cpp_typederefs : empty\n        | derefs '*'\n        | derefs '*' constopt_ref : '&'\n        | emptyclass_template : template_decl class_declclass_docstring : docstringclass_decl : class class_head opt_class_definition ';'class_head : scoped_name superclasses opt_annosstruct_decl : struct struct_head opt_class_definition ';'struct_head : scoped_name superclasses opt_annossuperclasses : ':' superclass_list\n        | emptysuperclass_list : superclass\n        | superclass_list ',' superclasssuperclass : class_access scoped_nameclass_access : empty\n        | public\n        | protected\n        | privateopt_class_definition : '{' opt_class_body '}'\n        | emptyopt_class_body : class_body\n        | emptyclass_body : class_line\n        | class_body class_lineclass_line : if_start\n        | if_end\n        | class_decl\n        | class_docstring\n        | class_template\n        | ctor\n        | dtor\n        | enum_decl\n        | exception\n        | typedef_decl\n        | method_variable\n        | namespace_decl\n        | struct_decl\n        | union_decl\n        | public_specifier\n        | protected_specifier\n        | private_specifier\n        | signals_specifier\n        | convert_from_type_code\n        | convert_to_subclass_code\n        | convert_to_type_code\n        | finalisation_code\n        | gc_clear_code\n        | gc_traverse_code\n        | get_buffer_code\n        | instance_code\n        | pickle_code\n        | property\n        | release_buffer_code\n        | type_code\n        | type_header_code\n        | type_hint_code\n        | BIGetReadBufferCode CODE_BLOCK\n        | BIGetWriteBufferCode CODE_BLOCK\n        | BIGetSegCountCode CODE_BLOCK\n        | BIGetCharBufferCode CODE_BLOCKctor : explicit ctor_decl\n        | ctor_declctor_decl : NAME '(' opt_arg_list ')' opt_exceptions opt_annos opt_ctor_signature ';' opt_docstring premethod_code method_codeopt_ctor_signature : '[' '(' opt_arg_list ')' ']'\n        | emptydtor : opt_virtual '~' NAME '(' ')' opt_exceptions opt_abstract opt_annos ';' premethod_code method_code virtual_catcher_codemethod_variable : Q_SIGNAL simple_method_variable\n        | Q_SLOT simple_method_variable\n        | simple_method_variablesimple_method_variable : virtual function\n        | static plain_method_variable\n        | plain_method_variableplain_method_variable : function\n        | variablepublic_specifier : public opt_slots ':'protected_specifier : protected opt_slots ':'private_specifier : private opt_slots ':'signals_specifier : signals ':'\n        | Q_SIGNALS ':'opt_slots : slots\n        | Q_SLOTS\n        | emptyenum_decl : enum opt_enum_key opt_name opt_annos '{' opt_enum_body '}' ';'opt_enum_key : class\n        | struct\n        | union\n        | emptyopt_enum_body : enum_body\n        | emptyenum_body : enum_line\n        | enum_body enum_lineenum_line : if_start\n        | if_end\n        | NAME opt_enum_assign opt_annos opt_commaopt_enum_assign : '=' value\n        | emptyopt_comma : empty\n        | ','exception : Exception scoped_name opt_base_exception opt_annos '{' exception_body '}' ';'opt_base_exception : '(' scoped_name ')'\n        | emptyexception_body : exception_line\n        | exception_body exception_lineexception_line : if_start\n        | if_end\n        | RaiseCode CODE_BLOCK\n        | TypeHeaderCode CODE_BLOCKfunction : function_decl\n        | assignment_operator_decl\n        | operator_decl\n        | operator_cast_declfunction_decl : cpp_type NAME '(' opt_arg_list ')' opt_const opt_final opt_exceptions opt_abstract opt_annos opt_signature ';' opt_docstring premethod_code method_code virtual_catcher_code virtual_call_codeassignment_operator_decl : cpp_type operator '=' '(' cpp_type ')' ';'operator_decl : cpp_type operator operator_name '(' opt_arg_list ')' opt_const opt_final opt_exceptions opt_abstract opt_annos opt_signature ';' premethod_code method_code virtual_catcher_code virtual_call_codeoperator_cast_decl : operator cpp_type '(' opt_arg_list ')' opt_const opt_final opt_exceptions opt_abstract opt_annos opt_signature ';' premethod_code method_code virtual_catcher_code virtual_call_codeopt_arg_list : arg_list\n        | emptyarg_list : arg_value\n        | arg_list ',' arg_valuearg_value : arg_type opt_assignarg_type : cpp_type opt_name opt_annosopt_assign : '=' expr\n        | emptyexpr : value\n        | expr binop valuevalue : opt_cast opt_unop simple_valuesimple_value : empty_value\n        | function_call_value\n        | null_value\n        | number_value\n        | quoted_char_value\n        | real_value\n        | scoped_name_value\n        | string_valueempty_value : '{' '}'function_call_value : base_type '(' opt_expr_list ')'null_value : NULLnumber_value : NUMBER\n        | bool_valuequoted_char_value : QUOTED_CHARreal_value : REALscoped_name_value : scoped_namestring_value : STRINGopt_expr_list : expr_list\n        | emptyexpr_list : expr\n        | expr_list ',' expropt_cast : '(' scoped_name ')'\n        | emptybinop : '-'\n        | '+'\n        | '*'\n        | '/'\n        | '&'\n        | '|'opt_unop : empty\n        | '!'\n        | '~'\n        | '-'\n        | '+'\n        | '*'\n        | '&'opt_exceptions : empty\n        | noexcept\n        | throw '(' opt_exception_list ')'opt_exception_list : exception_list\n        | emptyexception_list : scoped_name\n        | exception_list ',' scoped_nameopt_abstract : '=' NUMBER\n        | emptyopt_signature : '[' cpp_type '(' opt_arg_list ')' ']'\n        | emptyoperator_name : '+'\n        | '-'\n        | '*'\n        | '/'\n        | '%'\n        | '&'\n        | '|'\n        | '^'\n        | '<' '<'\n        | '>' '>'\n        | '+' '='\n        | '-' '='\n        | '*' '='\n        | '/' '='\n        | '%' '='\n        | '&' '='\n        | '|' '='\n        | '^' '='\n        | '<' '<' '='\n        | '>' '>' '='\n        | '~'\n        | '(' ')'\n        | '[' ']'\n        | '<'\n        | '<' '='\n        | '=' '='\n        | '!' '='\n        | '>'\n        | '>' '='method_code : MethodCode CODE_BLOCK\n        | emptypremethod_code : PreMethodCode CODE_BLOCK\n        | emptyvirtual_call_code : VirtualCallCode CODE_BLOCK\n        | emptyvirtual_catcher_code : VirtualCatcherCode CODE_BLOCK\n        | emptynamespace_decl : namespace namespace_head opt_namespace_body ';'namespace_head : scoped_name opt_annosopt_namespace_body : '{' namespace_body '}'\n        | emptynamespace_body : namespace_statement\n        | namespace_body namespace_statementtypedef_decl : typedef cpp_type NAME opt_annos ';' opt_docstring\n        | typedef cpp_type '(' '*' NAME ')' '(' cpp_types ')' opt_annos ';' opt_docstringunion_decl : union union_head opt_class_definition ';'union_head : scoped_name opt_annosvariable : cpp_type NAME opt_annos variable_body ';'variable_body : '{' variable_body_directives '}'\n        | emptyvariable_body_directives : variable_body_directive\n        | variable_body_directives variable_body_directivevariable_body_directive : if_start\n        | if_end\n        | AccessCode CODE_BLOCK\n        | GetCode CODE_BLOCK\n        | SetCode CODE_BLOCKopt_annos : '/' annotations '/'\n        | emptyannotations : annotation\n        | annotations ',' annotationannotation : NAME\n        | NAME '=' annotation_valueannotation_value : dotted_name\n        | STRING\n        | NUMBERscoped_name : SCOPE relative_scoped_name\n        | relative_scoped_namerelative_scoped_name : NAME\n        | relative_scoped_name SCOPE NAMEtemplate_decl : template '<' cpp_types '>'bool_value : true\n        | True\n        | false\n        | Falsedotted_name : NAME\n        | DOTTED_NAMEfile_path : NAME\n        | DOTTED_NAME\n        | FILE_PATHempty :opt_const : const\n        | emptyopt_docstring : docstring\n        | emptyopt_final : final\n        | emptyopt_name : NAME\n        | emptyopt_virtual : virtual\n        | emptyored_qualifiers : NAME\n        | '!' NAME\n        | ored_qualifiers LOGICAL_OR NAME\n        | ored_qualifiers LOGICAL_OR '!' NAMEqualifier_list : NAME\n        | qualifier_list NAMEqualifiers : ored_qualifiers\n        | opt_name '-' opt_name"
    
_lr_action_items = {'EOF':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[35,35,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'CompositeModule':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[49,49,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Copying':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[50,50,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'DefaultDocstringFormat':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[51,51,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'DefaultDocstringSignature':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[52,52,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'DefaultEncoding':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[53,53,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'DefaultMetatype':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[54,54,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'DefaultSupertype':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[55,55,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'ExportedHeaderCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[56,56,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'ExportedTypeHintCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[57,57,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Extract':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[58,58,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Feature':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[60,60,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'HideNamespace':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[61,61,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Import':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[63,63,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Include':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[64,64,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'InitialisationCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[65,65,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'License':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[66,66,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Module':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[69,69,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'ModuleCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[70,70,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'ModuleHeaderCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[71,71,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Platforms':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[72,72,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Plugin':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[73,73,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'PreInitialisationCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[74,74,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'PostInitialisationCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[75,75,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Timeline':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[76,76,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'TypeHintCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,275,327,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[77,77,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,77,-488,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,77,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'UnitCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[78,78,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'UnitPostIncludeCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[79,79,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'VirtualErrorHandler':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[80,80,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'If':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,162,163,164,166,167,169,170,171,173,174,175,181,200,213,215,216,217,224,240,241,242,243,244,245,246,247,248,249,250,251,258,259,260,261,266,275,287,291,327,328,329,330,331,332,352,357,370,371,372,373,375,376,378,379,380,381,382,383,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,497,523,529,564,566,568,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,622,623,626,627,630,632,633,634,635,636,651,652,654,667,670,673,677,678,679,680,681,688,690,707,708,709,714,716,717,718,719,720,721,722,723,731,732,733,736,737,738,739,757,763,767,768,777,778,780,782,783,784,805,812,813,814,815,816,817,830,831,832,833,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,867,872,873,874,879,893,894,895,906,908,912,913,914,915,916,917,921,926,927,931,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[81,81,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,81,81,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,81,-61,-91,81,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,81,-152,81,-166,-199,81,81,-477,-488,81,-62,-64,-65,-66,-109,-116,-139,-67,-69,-125,-192,-194,81,-167,-169,-170,-171,-172,-175,-193,-107,-251,81,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,81,-460,-253,81,-464,-63,-134,-135,-168,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,81,81,-461,-476,-499,-466,81,-469,-471,-472,-60,-81,-499,-103,-110,-117,-490,-491,-492,-493,-127,-165,-499,-320,-321,-322,81,-335,-337,-338,-499,81,-347,-349,-350,-462,-502,-503,-470,-473,-474,-475,-56,-92,-153,-200,-336,-499,-341,-348,-351,-352,-358,-72,-74,-76,-78,-80,-98,-328,-499,-340,-344,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-499,-339,-342,-343,-380,-179,-185,81,-52,-499,81,-187,-189,-190,-191,-499,-381,-499,-499,-188,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'End':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,162,163,164,166,167,169,170,171,173,174,175,181,200,213,215,216,217,224,240,241,242,243,244,245,246,247,248,249,250,251,258,259,260,261,266,275,287,291,327,328,329,330,331,332,352,357,370,371,372,373,375,376,378,379,380,381,382,383,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,497,523,529,564,566,568,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,622,623,626,627,630,632,633,634,635,636,651,652,654,667,670,673,677,678,679,680,681,688,690,707,708,709,714,716,717,718,719,720,721,722,723,731,732,733,736,737,738,739,757,763,767,768,777,778,780,782,783,784,805,812,813,814,815,816,817,830,831,832,833,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,867,872,873,874,879,893,894,895,906,908,912,913,914,915,916,917,921,926,927,931,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[82,82,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,82,82,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,82,-61,-91,82,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,82,-152,82,-166,-199,82,82,-477,-488,82,-62,-64,-65,-66,-109,-116,-139,-67,-69,-125,-192,-194,82,-167,-169,-170,-171,-172,-175,-193,-107,-251,82,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,82,-460,-253,82,-464,-63,-134,-135,-168,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,82,82,-461,-476,-499,-466,82,-469,-471,-472,-60,-81,-499,-103,-110,-117,-490,-491,-492,-493,-127,-165,-499,-320,-321,-322,82,-335,-337,-338,-499,82,-347,-349,-350,-462,-502,-503,-470,-473,-474,-475,-56,-92,-153,-200,-336,-499,-341,-348,-351,-352,-358,-72,-74,-76,-78,-80,-98,-328,-499,-340,-344,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-499,-339,-342,-343,-380,-179,-185,82,-52,-499,82,-187,-189,-190,-191,-499,-381,-499,-499,-188,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'class':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,84,85,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,275,287,327,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,448,450,454,470,472,473,486,487,488,492,523,525,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[83,83,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,83,184,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,83,83,-488,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,83,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,83,-307,-314,-318,-317,-319,-456,83,-460,-253,-464,-489,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'enum':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,162,163,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,240,241,242,243,244,245,246,247,248,249,250,251,258,259,261,266,275,287,327,352,357,370,371,372,373,375,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[85,85,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,85,85,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,85,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,85,-152,-166,-199,85,85,-488,-109,-116,-139,-67,-69,-125,-192,-194,-175,-193,-107,-251,85,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,85,-460,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'Exception':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,275,287,327,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[86,86,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,86,86,-488,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,86,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,86,-460,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'namespace':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,275,287,327,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[91,91,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,91,91,-488,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,91,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,91,-460,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'struct':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,85,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[92,92,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,185,-353,-354,-355,-356,194,194,194,194,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,194,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,194,-195,194,-485,-55,-61,-91,194,-152,-166,-199,92,92,194,194,-488,194,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,92,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,194,-314,194,194,-318,194,-317,-319,-456,92,-460,-253,194,194,-464,-134,-135,-269,-302,-303,-304,-305,-306,194,-312,194,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,194,-499,-60,-81,-499,-103,-110,-117,-127,194,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,194,-499,-395,-396,-397,-398,-399,-400,194,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,194,-499,194,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,194,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'typedef':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,275,287,327,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[93,93,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,93,93,-488,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,93,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,93,-460,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'union':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,85,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[95,95,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,186,-353,-354,-355,-356,195,195,195,195,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,195,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,195,-195,195,-485,-55,-61,-91,195,-152,-166,-199,95,95,195,195,-488,195,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,95,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,195,-314,195,195,-318,195,-317,-319,-456,95,-460,-253,195,195,-464,-134,-135,-269,-302,-303,-304,-305,-306,195,-312,195,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,195,-499,-60,-81,-499,-103,-110,-117,-127,195,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,195,-499,-395,-396,-397,-398,-399,-400,195,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,195,-499,195,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,195,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'TypeHeaderCode':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,162,163,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,240,241,242,243,244,245,246,247,248,249,250,251,258,259,261,266,275,287,327,352,357,370,371,372,373,375,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,623,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,720,721,722,723,731,732,733,757,763,767,768,782,783,784,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[96,96,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,96,96,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,96,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,96,-152,-166,-199,96,96,-488,-109,-116,-139,-67,-69,-125,-192,-194,-175,-193,-107,-251,96,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,96,-460,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,725,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,725,-347,-349,-350,-462,-502,-503,-56,-92,-153,-200,-348,-351,-352,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'MappedType':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,84,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,525,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[97,97,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,182,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-489,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'template':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,275,287,327,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,564,566,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[99,99,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,99,99,-488,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,99,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,99,-460,-253,-464,-134,-135,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'operator':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,62,82,87,88,89,90,94,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,192,199,200,202,203,206,207,208,209,210,211,212,213,215,217,224,259,261,266,275,287,296,297,319,320,321,322,325,326,327,350,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,452,453,454,455,469,470,471,472,473,486,487,488,492,523,524,527,564,566,588,590,591,592,593,594,598,599,600,607,608,609,610,611,612,613,614,616,617,618,619,626,630,632,651,652,654,667,670,673,681,688,690,707,708,709,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[100,100,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-207,-108,-353,-354,-355,-356,197,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-209,-210,-195,-499,-244,-499,-217,-214,-215,-216,-213,-212,-485,-55,-61,-91,-152,-166,-199,100,100,-209,-210,-205,-245,-247,-248,-499,-211,-488,-208,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,100,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-487,100,-314,100,100,-318,100,-317,-319,-456,100,-460,-253,-464,-246,-204,-134,-135,-269,-302,-303,-304,-305,-306,-312,100,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,197,-316,-461,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-320,-321,-322,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'const':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,100,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,200,204,213,215,217,224,255,259,261,266,275,287,299,320,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,641,642,650,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,751,757,763,767,768,785,805,812,813,814,815,816,817,818,830,833,867,893,894,908,909,917,919,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[101,101,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,101,101,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,101,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,101,-485,-55,-61,-91,101,-152,-166,-199,101,101,101,524,101,-488,101,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,101,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,101,-314,101,101,-318,101,-317,-319,-456,101,-460,-253,101,101,-464,-134,-135,-269,-302,-303,-304,-305,-306,101,-312,101,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,742,101,742,-60,-81,-499,-103,-110,-117,-127,101,-165,-499,-320,-321,-322,-462,-502,-503,742,-56,-92,-153,-200,101,-358,-72,-74,-76,-78,-80,-98,742,-328,-344,-499,-179,-185,-499,101,-499,101,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,101,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'unsigned':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[103,103,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,103,103,103,103,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,103,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,103,-195,103,-485,-55,-61,-91,103,-152,-166,-199,103,103,103,103,-488,103,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,103,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,103,-314,103,103,-318,103,-317,-319,-456,103,-460,-253,103,103,-464,-134,-135,-269,-302,-303,-304,-305,-306,103,-312,103,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,103,-499,-60,-81,-499,-103,-110,-117,-127,103,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,103,-499,-395,-396,-397,-398,-399,-400,103,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,103,-499,103,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,103,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'signed':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[105,105,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,105,105,105,105,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,105,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,105,-195,105,-485,-55,-61,-91,105,-152,-166,-199,105,105,105,105,-488,105,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,105,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,105,-314,105,105,-318,105,-317,-319,-456,105,-460,-253,105,105,-464,-134,-135,-269,-302,-303,-304,-305,-306,105,-312,105,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,105,-499,-60,-81,-499,-103,-110,-117,-127,105,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,105,-499,-395,-396,-397,-398,-399,-400,105,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,105,-499,105,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,105,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'long':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,103,104,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,207,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[104,104,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,104,104,104,104,207,211,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,104,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,104,-195,104,326,-485,-55,-61,-91,104,-152,-166,-199,104,104,104,104,-488,104,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,104,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,104,-314,104,104,-318,104,-317,-319,-456,104,-460,-253,104,104,-464,-134,-135,-269,-302,-303,-304,-305,-306,104,-312,104,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,104,-499,-60,-81,-499,-103,-110,-117,-127,104,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,104,-499,-395,-396,-397,-398,-399,-400,104,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,104,-499,104,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,104,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'short':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,103,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[107,107,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,107,107,107,107,209,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,107,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,107,-195,107,-485,-55,-61,-91,107,-152,-166,-199,107,107,107,107,-488,107,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,107,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,107,-314,107,107,-318,107,-317,-319,-456,107,-460,-253,107,107,-464,-134,-135,-269,-302,-303,-304,-305,-306,107,-312,107,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,107,-499,-60,-81,-499,-103,-110,-117,-127,107,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,107,-499,-395,-396,-397,-398,-399,-400,107,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,107,-499,107,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,107,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'int':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,103,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[108,108,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,108,108,108,108,210,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,108,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,108,-195,108,-485,-55,-61,-91,108,-152,-166,-199,108,108,108,108,-488,108,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,108,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,108,-314,108,108,-318,108,-317,-319,-456,108,-460,-253,108,108,-464,-134,-135,-269,-302,-303,-304,-305,-306,108,-312,108,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,108,-499,-60,-81,-499,-103,-110,-117,-127,108,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,108,-499,-395,-396,-397,-398,-399,-400,108,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,108,-499,108,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,108,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'float':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[109,109,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,109,109,109,109,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,109,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,109,-195,109,-485,-55,-61,-91,109,-152,-166,-199,109,109,109,109,-488,109,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,109,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,109,-314,109,109,-318,109,-317,-319,-456,109,-460,-253,109,109,-464,-134,-135,-269,-302,-303,-304,-305,-306,109,-312,109,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,109,-499,-60,-81,-499,-103,-110,-117,-127,109,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,109,-499,-395,-396,-397,-398,-399,-400,109,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,109,-499,109,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,109,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'double':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[110,110,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,110,110,110,110,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,110,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,110,-195,110,-485,-55,-61,-91,110,-152,-166,-199,110,110,110,110,-488,110,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,110,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,110,-314,110,110,-318,110,-317,-319,-456,110,-460,-253,110,110,-464,-134,-135,-269,-302,-303,-304,-305,-306,110,-312,110,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,110,-499,-60,-81,-499,-103,-110,-117,-127,110,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,110,-499,-395,-396,-397,-398,-399,-400,110,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,110,-499,110,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,110,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'bool':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[111,111,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,111,111,111,111,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,111,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,111,-195,111,-485,-55,-61,-91,111,-152,-166,-199,111,111,111,111,-488,111,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,111,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,111,-314,111,111,-318,111,-317,-319,-456,111,-460,-253,111,111,-464,-134,-135,-269,-302,-303,-304,-305,-306,111,-312,111,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,111,-499,-60,-81,-499,-103,-110,-117,-127,111,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,111,-499,-395,-396,-397,-398,-399,-400,111,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,111,-499,111,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,111,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'char':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,103,105,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[106,106,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,106,106,106,106,208,212,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,106,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,106,-195,106,-485,-55,-61,-91,106,-152,-166,-199,106,106,106,106,-488,106,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,106,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,106,-314,106,106,-318,106,-317,-319,-456,106,-460,-253,106,106,-464,-134,-135,-269,-302,-303,-304,-305,-306,106,-312,106,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,106,-499,-60,-81,-499,-103,-110,-117,-127,106,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,106,-499,-395,-396,-397,-398,-399,-400,106,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,106,-499,106,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,106,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'wchar_t':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[112,112,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,112,112,112,112,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,112,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,112,-195,112,-485,-55,-61,-91,112,-152,-166,-199,112,112,112,112,-488,112,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,112,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,112,-314,112,112,-318,112,-317,-319,-456,112,-460,-253,112,112,-464,-134,-135,-269,-302,-303,-304,-305,-306,112,-312,112,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,112,-499,-60,-81,-499,-103,-110,-117,-127,112,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,112,-499,-395,-396,-397,-398,-399,-400,112,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,112,-499,112,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,112,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'void':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[113,113,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,113,113,113,113,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,113,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,113,-195,113,-485,-55,-61,-91,113,-152,-166,-199,113,113,113,113,-488,113,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,113,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,113,-314,113,113,-318,113,-317,-319,-456,113,-460,-253,113,113,-464,-134,-135,-269,-302,-303,-304,-305,-306,113,-312,113,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,113,-499,-60,-81,-499,-103,-110,-117,-127,113,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,113,-499,-395,-396,-397,-398,-399,-400,113,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,113,-499,113,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,113,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYOBJECT':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[114,114,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,114,114,114,114,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,114,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,114,-195,114,-485,-55,-61,-91,114,-152,-166,-199,114,114,114,114,-488,114,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,114,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,114,-314,114,114,-318,114,-317,-319,-456,114,-460,-253,114,114,-464,-134,-135,-269,-302,-303,-304,-305,-306,114,-312,114,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,114,-499,-60,-81,-499,-103,-110,-117,-127,114,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,114,-499,-395,-396,-397,-398,-399,-400,114,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,114,-499,114,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,114,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYTUPLE':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[115,115,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,115,115,115,115,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,115,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,115,-195,115,-485,-55,-61,-91,115,-152,-166,-199,115,115,115,115,-488,115,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,115,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,115,-314,115,115,-318,115,-317,-319,-456,115,-460,-253,115,115,-464,-134,-135,-269,-302,-303,-304,-305,-306,115,-312,115,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,115,-499,-60,-81,-499,-103,-110,-117,-127,115,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,115,-499,-395,-396,-397,-398,-399,-400,115,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,115,-499,115,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,115,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYLIST':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[116,116,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,116,116,116,116,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,116,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,116,-195,116,-485,-55,-61,-91,116,-152,-166,-199,116,116,116,116,-488,116,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,116,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,116,-314,116,116,-318,116,-317,-319,-456,116,-460,-253,116,116,-464,-134,-135,-269,-302,-303,-304,-305,-306,116,-312,116,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,116,-499,-60,-81,-499,-103,-110,-117,-127,116,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,116,-499,-395,-396,-397,-398,-399,-400,116,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,116,-499,116,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,116,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYDICT':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[117,117,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,117,117,117,117,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,117,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,117,-195,117,-485,-55,-61,-91,117,-152,-166,-199,117,117,117,117,-488,117,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,117,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,117,-314,117,117,-318,117,-317,-319,-456,117,-460,-253,117,117,-464,-134,-135,-269,-302,-303,-304,-305,-306,117,-312,117,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,117,-499,-60,-81,-499,-103,-110,-117,-127,117,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,117,-499,-395,-396,-397,-398,-399,-400,117,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,117,-499,117,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,117,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYCALLABLE':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[118,118,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,118,118,118,118,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,118,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,118,-195,118,-485,-55,-61,-91,118,-152,-166,-199,118,118,118,118,-488,118,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,118,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,118,-314,118,118,-318,118,-317,-319,-456,118,-460,-253,118,118,-464,-134,-135,-269,-302,-303,-304,-305,-306,118,-312,118,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,118,-499,-60,-81,-499,-103,-110,-117,-127,118,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,118,-499,-395,-396,-397,-398,-399,-400,118,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,118,-499,118,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,118,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYSLICE':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[119,119,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,119,119,119,119,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,119,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,119,-195,119,-485,-55,-61,-91,119,-152,-166,-199,119,119,119,119,-488,119,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,119,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,119,-314,119,119,-318,119,-317,-319,-456,119,-460,-253,119,119,-464,-134,-135,-269,-302,-303,-304,-305,-306,119,-312,119,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,119,-499,-60,-81,-499,-103,-110,-117,-127,119,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,119,-499,-395,-396,-397,-398,-399,-400,119,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,119,-499,119,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,119,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYTYPE':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[120,120,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,120,120,120,120,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,120,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,120,-195,120,-485,-55,-61,-91,120,-152,-166,-199,120,120,120,120,-488,120,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,120,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,120,-314,120,120,-318,120,-317,-319,-456,120,-460,-253,120,120,-464,-134,-135,-269,-302,-303,-304,-305,-306,120,-312,120,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,120,-499,-60,-81,-499,-103,-110,-117,-127,120,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,120,-499,-395,-396,-397,-398,-399,-400,120,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,120,-499,120,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,120,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYBUFFER':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[121,121,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,121,121,121,121,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,121,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,121,-195,121,-485,-55,-61,-91,121,-152,-166,-199,121,121,121,121,-488,121,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,121,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,121,-314,121,121,-318,121,-317,-319,-456,121,-460,-253,121,121,-464,-134,-135,-269,-302,-303,-304,-305,-306,121,-312,121,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,121,-499,-60,-81,-499,-103,-110,-117,-127,121,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,121,-499,-395,-396,-397,-398,-399,-400,121,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,121,-499,121,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,121,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_PYENUM':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[122,122,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,122,122,122,122,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,122,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,122,-195,122,-485,-55,-61,-91,122,-152,-166,-199,122,122,122,122,-488,122,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,122,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,122,-314,122,122,-318,122,-317,-319,-456,122,-460,-253,122,122,-464,-134,-135,-269,-302,-303,-304,-305,-306,122,-312,122,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,122,-499,-60,-81,-499,-103,-110,-117,-127,122,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,122,-499,-395,-396,-397,-398,-399,-400,122,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,122,-499,122,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,122,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SIP_SSIZE_T':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[123,123,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,123,123,123,123,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,123,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,123,-195,123,-485,-55,-61,-91,123,-152,-166,-199,123,123,123,123,-488,123,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,123,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,123,-314,123,123,-318,123,-317,-319,-456,123,-460,-253,123,123,-464,-134,-135,-269,-302,-303,-304,-305,-306,123,-312,123,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,123,-499,-60,-81,-499,-103,-110,-117,-127,123,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,123,-499,-395,-396,-397,-398,-399,-400,123,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,123,-499,123,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,123,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Py_hash_t':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[124,124,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,124,124,124,124,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,124,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,124,-195,124,-485,-55,-61,-91,124,-152,-166,-199,124,124,124,124,-488,124,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,124,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,124,-314,124,124,-318,124,-317,-319,-456,124,-460,-253,124,124,-464,-134,-135,-269,-302,-303,-304,-305,-306,124,-312,124,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,124,-499,-60,-81,-499,-103,-110,-117,-127,124,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,124,-499,-395,-396,-397,-398,-399,-400,124,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,124,-499,124,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,124,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Py_ssize_t':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[125,125,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,125,125,125,125,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,125,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,125,-195,125,-485,-55,-61,-91,125,-152,-166,-199,125,125,125,125,-488,125,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,125,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,125,-314,125,125,-318,125,-317,-319,-456,125,-460,-253,125,125,-464,-134,-135,-269,-302,-303,-304,-305,-306,125,-312,125,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,125,-499,-60,-81,-499,-103,-110,-117,-127,125,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,125,-499,-395,-396,-397,-398,-399,-400,125,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,125,-499,125,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,125,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'size_t':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[126,126,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,126,126,126,126,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,126,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,126,-195,126,-485,-55,-61,-91,126,-152,-166,-199,126,126,126,126,-488,126,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,126,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,126,-314,126,126,-318,126,-317,-319,-456,126,-460,-253,126,126,-464,-134,-135,-269,-302,-303,-304,-305,-306,126,-312,126,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,126,-499,-60,-81,-499,-103,-110,-117,-127,126,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,126,-499,-395,-396,-397,-398,-399,-400,126,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,126,-499,126,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,126,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'ELLIPSIS':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,93,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,200,204,213,215,217,224,255,259,261,266,275,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,469,470,471,472,473,486,487,488,492,506,508,523,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,867,880,893,894,901,908,909,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[127,127,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,127,127,127,127,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,127,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,127,-195,127,-485,-55,-61,-91,127,-152,-166,-199,127,127,127,127,-488,127,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,127,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,127,-314,127,127,-318,127,-317,-319,-456,127,-460,-253,127,127,-464,-134,-135,-269,-302,-303,-304,-305,-306,127,-312,127,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-461,-499,-466,127,-499,-60,-81,-499,-103,-110,-117,-127,127,-165,-499,-320,-321,-322,-462,-502,-503,-499,-394,-56,-92,-153,-200,-499,127,-499,-395,-396,-397,-398,-399,-400,127,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,-499,-499,-179,-185,-394,-499,127,-499,127,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,127,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'SCOPE':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,61,82,83,86,87,88,89,90,91,92,93,95,97,100,101,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,159,160,164,166,167,169,170,171,173,174,175,181,182,194,195,200,204,213,215,217,224,255,259,261,266,275,278,284,287,299,324,327,351,352,357,371,372,373,376,396,398,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,452,453,454,455,469,470,471,472,473,477,478,479,480,481,486,487,488,492,506,508,523,549,564,566,588,590,591,592,593,594,597,598,599,600,607,608,609,610,611,612,613,614,616,617,619,620,626,630,632,642,644,651,652,654,667,670,673,681,687,688,690,707,708,709,731,732,733,747,748,749,757,763,767,768,779,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,830,833,855,866,867,880,893,894,901,908,909,911,917,919,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[128,128,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,128,-108,128,128,-353,-354,-355,-356,128,128,128,128,128,128,128,214,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,128,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,128,128,128,-195,128,-485,-55,-61,-91,128,-152,-166,-199,128,-499,128,128,128,128,-488,128,-109,-116,-67,-69,-125,-194,-175,-193,-107,-251,128,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-487,128,-314,128,128,-318,128,-317,-319,128,-260,-261,-262,-263,-456,128,-460,-253,128,128,-464,128,-134,-135,-269,-302,-303,-304,-305,-306,128,-312,128,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-461,-499,-466,128,-499,-60,-81,-499,-103,-110,-117,-127,128,-165,-499,-320,-321,-322,-462,-502,-503,-499,128,-394,-56,-92,-153,-200,-499,128,-499,-395,-396,-397,-398,-399,-400,128,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,-328,-344,-393,128,-499,-499,-179,-185,-394,-499,128,128,-499,128,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,128,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'NAME':([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,54,55,58,59,60,61,62,63,64,69,73,80,82,83,85,86,87,88,89,90,91,92,93,94,95,97,98,100,101,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,154,155,157,159,160,164,166,167,168,169,170,171,172,173,174,175,178,181,182,183,184,185,186,187,192,193,194,195,199,200,202,203,204,206,207,208,209,210,211,212,213,214,215,217,224,255,259,261,263,264,265,266,272,275,278,284,287,290,291,296,297,299,319,320,321,322,324,325,326,327,350,351,352,357,371,372,373,374,376,396,397,398,402,403,404,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,449,450,452,453,454,455,469,470,471,472,473,477,478,479,480,481,486,487,488,492,495,499,506,508,523,524,527,536,540,541,544,546,549,553,556,564,566,574,577,583,585,588,590,591,592,593,594,596,597,598,599,600,607,608,609,610,611,612,613,614,616,617,618,619,620,622,626,627,628,629,630,632,642,644,651,652,654,667,670,673,677,678,679,680,681,687,688,690,707,708,709,714,716,717,718,719,731,732,733,747,748,749,757,763,767,768,777,778,779,780,785,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,805,812,813,814,815,816,817,827,828,829,830,831,832,833,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,855,866,867,872,873,874,879,880,893,894,901,908,909,911,917,919,921,922,926,927,932,933,937,939,941,942,944,945,946,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[59,59,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,133,133,133,148,-487,150,59,-207,-51,-51,133,169,176,-108,59,-499,59,-353,-354,-355,-356,59,59,59,196,59,59,-499,59,59,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,59,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,59,232,232,-124,-126,-499,-173,-151,264,-176,-178,-177,264,-196,-197,-198,271,-249,59,282,-329,-330,-331,-332,-209,294,59,59,-210,-195,-499,-244,59,-499,-217,-214,-215,-216,-213,-212,-485,327,-55,-61,-91,59,-152,-166,397,-514,397,-199,405,452,-499,59,59,491,-477,-209,-210,59,-205,-245,-247,-248,59,-499,-211,-488,-208,59,-109,-116,-67,-69,-125,565,-194,-175,-515,-193,-107,584,282,-251,452,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,595,-307,-487,59,-314,59,59,-318,59,-317,-319,59,-260,-261,-262,-263,-456,59,-460,-253,631,282,59,59,-464,-246,-204,133,133,133,664,666,59,232,232,-134,-135,694,133,703,704,-269,-302,-303,-304,-305,-306,705,59,-312,59,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,711,-316,-499,719,-461,-476,491,133,-499,-466,59,-499,-60,-81,-499,-103,-110,-117,-490,-491,-492,-493,-127,59,-165,-499,-320,-321,-322,719,-335,-337,-338,-499,-462,-502,-503,-499,59,-394,-56,-92,-153,-200,-336,-499,-499,-341,59,-499,-395,-396,-397,-398,-399,-400,59,-401,-402,-403,-404,-405,-406,-407,-358,-72,-74,-76,-78,-80,-98,869,870,871,-328,-499,-340,-344,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-393,59,-499,-339,-342,-343,-380,-499,-179,-185,-394,-499,59,59,-499,59,-381,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,59,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'$end':([1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,130,131,133,134,135,136,138,140,142,144,146,147,150,152,159,160,164,166,167,169,170,171,173,174,175,181,200,213,215,217,224,259,261,266,327,352,357,396,398,402,406,486,492,523,564,566,630,632,651,652,654,667,670,673,681,688,690,731,732,733,757,763,767,768,805,812,813,814,815,816,817,830,833,917,932,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,974,976,977,978,979,],[0,-1,-3,-4,-5,-6,-7,-8,-9,-10,-11,-12,-13,-14,-15,-16,-17,-18,-19,-20,-21,-22,-23,-24,-25,-26,-27,-28,-29,-30,-31,-32,-33,-34,-48,-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-2,-499,-494,-495,-70,-71,-73,-75,-77,-79,-89,-90,-97,-102,-124,-126,-499,-173,-151,-176,-178,-177,-196,-197,-198,-249,-195,-485,-55,-61,-91,-152,-166,-199,-488,-109,-116,-175,-193,-107,-251,-456,-253,-464,-134,-135,-499,-466,-60,-81,-499,-103,-110,-117,-127,-165,-499,-462,-502,-503,-56,-92,-153,-200,-358,-72,-74,-76,-78,-80,-98,-328,-344,-499,-463,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'}':([36,37,38,39,40,41,42,43,44,45,46,47,48,59,82,87,88,89,90,129,173,181,200,213,240,241,242,243,244,245,246,247,248,249,250,251,258,263,264,265,275,291,327,328,329,330,331,332,370,371,372,373,375,376,378,379,380,381,382,383,397,402,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,487,488,492,523,529,568,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,622,626,627,630,632,633,634,635,636,652,677,678,679,680,707,708,709,713,714,715,716,717,718,719,720,721,722,723,731,732,733,736,737,738,739,777,778,780,782,783,784,805,830,831,832,833,837,838,839,840,841,842,843,844,845,846,848,849,850,851,852,853,854,867,872,873,874,879,893,894,906,908,912,913,914,915,916,917,921,926,927,931,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[-35,-36,-37,-38,-39,-40,-41,-42,-43,-44,-45,-46,-47,-487,-108,-353,-354,-355,-356,-486,-196,-249,-195,-485,369,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,377,396,-514,398,-499,-477,-488,528,-62,-64,-65,-66,-139,-67,-69,-125,-192,-194,567,-167,-169,-170,-171,-172,-515,-107,-251,587,-266,-267,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,625,-460,-253,-464,-63,-168,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-461,-476,-499,-466,735,-469,-471,-472,-81,-490,-491,-492,-493,-320,-321,-322,776,-333,-334,-335,-337,-338,-499,781,-347,-349,-350,-462,-502,-503,-470,-473,-474,-475,-336,-499,-341,-348,-351,-352,-358,-328,-499,-340,-344,-371,-372,-373,-374,-375,-376,-377,-378,-379,879,-382,-383,-384,-385,-386,-387,-388,-499,-339,-342,-343,-380,-179,-185,-52,-499,930,-187,-189,-190,-191,-499,-381,-499,-499,-188,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'DOTTED_NAME':([49,54,55,63,64,69,155,157,536,540,541,553,556,577,629,],[134,134,134,-51,-51,134,233,233,134,134,134,233,233,134,134,]),'(':([49,51,52,53,54,55,58,59,60,61,62,63,64,66,69,80,81,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,132,137,139,141,143,145,149,151,153,156,158,161,165,177,188,193,196,197,202,203,205,206,207,208,209,210,211,212,213,296,297,300,302,303,304,305,306,307,308,309,310,311,312,313,319,320,321,322,325,326,327,333,350,384,452,467,505,507,509,510,511,512,513,514,515,516,517,518,519,520,521,522,524,527,533,565,569,595,615,644,648,649,705,711,734,779,789,790,791,792,793,794,795,824,847,853,880,887,922,934,],[-49,-49,-49,-49,-49,-49,-49,-487,-49,-49,-207,-49,-49,-49,-49,-49,178,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,218,219,220,221,222,223,225,226,227,235,238,239,262,267,284,295,299,301,-499,-244,324,-499,-217,-214,-215,-216,-213,-212,-485,-209,-210,506,508,-419,-420,-421,-422,-423,-424,-425,-426,-442,-446,-439,-205,-245,-247,-248,-499,-211,-488,-49,-208,-49,597,-49,-444,-440,-429,-430,-431,-432,-433,-434,-435,-436,-427,-443,-428,-447,-441,-445,-246,-204,653,687,689,597,710,748,-437,-438,769,299,785,748,748,-395,-396,-397,-398,-399,-400,866,880,-207,748,909,748,946,]),'CODE_BLOCK':([50,56,57,65,70,71,74,75,77,78,79,96,148,176,252,253,254,256,257,333,443,444,445,446,461,462,463,464,465,466,468,530,531,532,637,638,639,662,701,724,725,858,940,951,962,975,],[135,146,147,159,166,167,170,171,173,174,175,200,224,266,371,372,373,375,376,-499,590,591,592,593,609,610,611,612,613,614,616,652,-82,-83,737,738,739,763,768,783,784,-84,953,964,970,979,]),'STRING':([51,52,53,66,333,537,538,539,560,561,562,563,575,576,629,644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,810,811,819,855,880,901,922,],[136,138,140,160,532,657,658,659,683,684,685,686,695,696,729,-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,854,-401,-402,-403,-404,-405,-406,-407,860,861,863,-393,-499,-394,-499,]),'<':([59,62,99,129,197,213,311,327,452,853,],[-487,154,204,-486,311,-485,517,-488,-487,154,]),'*':([59,62,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,192,197,199,202,203,206,207,208,209,210,211,212,213,295,296,297,320,325,326,327,350,452,524,644,677,678,679,680,745,746,747,749,779,789,790,791,792,793,794,795,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,855,879,880,901,902,921,922,935,],[-487,-207,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,-209,305,-210,320,-244,-499,-217,-214,-215,-216,-213,-212,-485,495,-209,-210,-245,320,-211,-488,-208,-487,-246,-499,-490,-491,-492,-493,792,-369,802,-394,-499,-499,-395,-396,-397,-398,-399,-400,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-393,-380,-499,-394,792,-381,-499,792,]),'&':([59,62,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,192,197,199,202,203,206,207,208,209,210,211,212,213,296,297,320,325,326,327,350,452,524,644,677,678,679,680,745,746,747,749,779,789,790,791,792,793,794,795,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,855,879,880,901,902,921,922,935,],[-487,-207,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,-209,308,-210,321,-244,-499,-217,-214,-215,-216,-213,-212,-485,-209,-210,-245,321,-211,-488,-208,-487,-246,-499,-490,-491,-492,-493,794,-369,803,-394,-499,-499,-395,-396,-397,-398,-399,-400,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-393,-380,-499,-394,794,-381,-499,794,]),':':([59,129,180,192,213,327,456,457,458,459,460,601,602,603,604,605,606,],[-487,-486,278,278,-485,-488,-499,-499,-499,607,608,707,-325,-326,-327,708,709,]),'/':([59,62,85,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,133,134,180,183,184,185,186,187,188,190,192,196,197,199,201,202,203,206,207,208,209,210,211,212,213,273,277,279,280,281,282,283,285,293,294,296,297,319,320,321,322,325,326,327,350,475,476,489,490,491,499,524,527,621,624,640,641,650,677,678,679,680,712,719,726,727,728,729,730,741,742,743,745,746,751,752,770,778,780,786,787,788,806,807,818,820,821,822,823,832,835,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,856,857,862,864,875,876,878,879,881,882,883,885,898,902,903,910,921,935,],[-487,-207,-499,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,-494,-495,-499,-499,-329,-330,-331,-332,-499,290,-499,290,306,290,290,-499,-244,-499,-217,-214,-215,-216,-213,-212,-485,-507,290,-256,290,290,-506,290,-346,290,290,-209,-210,-205,-245,-247,-248,-499,-211,-488,-208,-255,-257,627,-478,-480,-499,-246,-204,-259,-345,290,-499,-499,-490,-491,-492,-493,-258,-499,-479,-481,-482,-483,-484,-499,-500,-501,793,-369,-499,-499,-499,290,-341,-499,-504,-505,-499,-499,-499,-499,290,-408,-409,-340,-499,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-499,-499,-499,-499,290,290,-416,-380,-499,290,290,290,-415,793,290,-410,-381,793,]),'{':([59,62,67,68,72,76,85,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,131,133,134,164,179,180,183,184,185,186,187,188,189,190,191,192,196,198,199,201,207,208,209,210,211,212,213,273,277,279,280,281,282,283,285,289,291,293,296,297,298,317,318,326,327,350,474,475,476,482,483,484,493,621,624,627,644,654,690,712,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,867,880,901,922,],[-487,-207,162,163,168,172,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,216,-494,-495,260,275,-499,-499,-329,-330,-331,-332,-499,287,-499,275,-499,-499,275,-499,-499,-217,-214,-215,-216,-213,-212,-485,-507,-499,-256,-499,-499,-506,-499,-346,-457,-477,-499,-209,-210,497,-465,-136,-211,-488,-208,-252,-255,-257,-137,622,623,-254,-259,-345,-476,-499,216,260,-258,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,846,-401,-402,-403,-404,-405,-406,-407,-393,895,-499,-394,-499,]),';':([59,129,179,180,189,190,191,192,196,198,199,213,274,276,277,279,286,288,289,291,292,293,294,298,316,317,327,369,377,474,475,476,493,494,496,498,528,567,587,621,625,627,641,650,712,735,741,742,743,750,751,752,770,776,781,786,787,788,806,807,818,820,821,822,823,835,856,857,862,864,865,875,876,878,881,882,883,885,886,888,896,897,898,903,904,905,907,910,918,920,923,924,925,930,936,955,972,],[-487,-486,-499,-499,-499,-499,-499,-499,-499,-499,-499,-485,406,-265,-499,-256,486,-459,-457,-477,492,-499,-499,-499,523,-465,-488,564,566,-252,-255,-257,-254,630,632,-468,651,688,-264,-259,-458,-476,-499,-499,-258,-467,-499,-500,-501,805,-499,-499,-499,830,833,-499,-504,-505,-499,-499,-499,-499,-499,-408,-409,-499,-499,-499,-499,-499,-499,-499,-499,-416,-499,-499,-499,-499,908,-310,917,-499,-415,-499,-499,-499,926,-410,933,-418,-499,937,938,944,947,-309,-417,]),'>':([59,62,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,197,202,203,206,207,208,209,210,211,212,213,228,229,296,297,312,319,320,321,322,323,325,326,327,350,524,527,550,],[-487,-207,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,312,-499,-244,-499,-217,-214,-215,-216,-213,-212,-485,350,-242,-209,-210,519,-205,-245,-247,-248,525,-499,-211,-488,-208,-246,-204,-243,]),',':([59,62,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,133,134,202,203,206,207,208,209,210,211,212,213,228,229,232,233,234,273,282,291,296,297,319,320,321,322,323,325,326,327,334,335,342,343,347,348,350,354,355,359,360,363,364,385,386,399,400,475,476,489,490,491,499,501,503,504,524,527,550,621,627,640,643,645,655,656,663,664,665,668,669,671,672,674,675,676,677,678,679,680,682,683,684,685,686,691,692,693,694,695,696,697,698,699,700,702,703,712,719,726,727,728,729,730,740,744,745,746,753,754,771,772,778,780,831,832,834,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,859,860,861,868,869,870,871,879,890,892,900,902,921,929,935,],[-487,-207,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,-494,-495,-499,-244,-499,-217,-214,-215,-216,-213,-212,-485,351,-242,-496,-497,-498,-507,-506,-477,-209,-210,-205,-245,-247,-248,351,-499,-211,-488,535,-57,543,-93,548,-104,-208,552,-113,555,-120,559,-128,571,-154,582,-201,620,-257,628,-478,-480,-499,642,-363,-499,-246,-204,-243,-259,-476,-499,-365,-368,-58,-59,-94,-95,-96,-105,-106,-114,-115,-121,-122,-123,-490,-491,-492,-493,-129,-130,-131,-132,-133,-155,-156,-157,-158,-159,-160,-161,-162,-163,-164,-202,-203,-258,-499,-479,-481,-482,-483,-484,-366,-364,-367,-369,809,-85,826,-180,-499,-341,874,-340,351,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-86,-87,-88,-181,-182,-183,-184,-380,911,-413,922,-391,-381,-414,-392,]),'=':([59,62,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,197,202,203,206,207,208,209,210,211,212,213,273,282,291,296,297,300,303,304,305,306,307,308,309,310,311,312,315,319,320,321,322,325,326,327,336,337,338,339,340,341,344,345,346,349,350,356,361,362,365,366,367,368,387,388,389,390,391,392,393,394,395,401,491,499,504,517,519,524,527,627,640,641,650,719,740,741,742,743,751,752,755,756,766,773,774,775,786,787,788,806,807,820,822,823,835,856,857,864,881,910,],[-487,-207,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,300,-499,-244,-499,-217,-214,-215,-216,-213,-212,-485,-507,-506,-477,-209,-210,505,509,510,511,512,513,514,515,516,518,520,522,-205,-245,-247,-248,-499,-211,-488,536,537,538,539,540,541,544,545,546,549,-208,553,556,557,560,561,562,563,572,573,574,575,576,577,578,579,580,583,629,-499,644,648,649,-246,-204,-476,-499,-499,-499,779,-366,-499,-500,-501,-499,-499,810,811,819,827,828,829,-499,-504,-505,-499,-499,-499,-408,-409,877,-499,877,877,877,-410,]),')':([59,62,98,102,103,104,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,129,133,134,202,203,206,207,208,209,210,211,212,213,229,232,233,234,268,269,271,273,282,291,296,297,299,301,319,320,321,322,324,325,326,327,334,335,342,343,347,348,350,353,354,355,358,359,360,363,364,385,386,399,400,404,405,485,499,500,501,502,503,504,508,524,526,527,534,542,547,550,551,554,558,570,581,584,586,597,627,631,640,643,645,646,647,655,656,657,658,659,660,661,663,664,665,666,668,669,671,672,674,675,676,677,678,679,680,682,683,684,685,686,687,691,692,693,694,695,696,697,698,699,700,702,703,704,706,740,744,745,746,753,754,758,759,760,761,762,764,765,769,771,772,804,808,825,834,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,859,860,861,863,866,868,869,870,871,879,880,884,889,890,891,892,899,900,901,902,909,921,928,929,935,946,957,],[-487,-207,-499,-206,-218,-221,-225,-219,-220,-222,-223,-224,-226,-227,-228,-229,-230,-231,-232,-233,-234,-235,-236,-237,-238,-239,-240,-241,-486,-494,-495,-499,-244,-499,-217,-214,-215,-216,-213,-212,-485,-242,-496,-497,-498,402,-516,-510,-507,-506,-477,-209,-210,-499,507,-205,-245,-247,-248,-499,-499,-211,-488,-50,-57,-50,-93,-50,-104,-208,-50,-112,-113,-50,-119,-120,-50,-128,-50,-154,-50,-201,-499,-511,624,-499,641,-361,-362,-363,-499,-499,-246,650,-204,654,662,667,-243,670,673,681,690,701,-512,-517,-499,-476,734,-499,-365,-368,750,751,-58,-59,-50,-50,-50,-50,-50,-94,-95,-96,-50,-105,-106,-114,-115,-121,-122,-123,-490,-491,-492,-493,-129,-130,-131,-132,-133,-499,-155,-156,-157,-158,-159,-160,-161,-162,-163,-164,-202,-203,-513,770,-366,-364,-367,-369,-50,-85,812,813,814,815,816,817,818,820,-50,-180,855,858,867,875,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-86,-87,-88,-50,-499,-181,-182,-183,-184,-380,-499,906,910,-411,-412,-413,921,-389,-390,-391,-499,-381,943,-414,-392,-499,966,]),'-':([59,129,178,197,213,270,271,273,327,644,677,678,679,680,745,746,747,749,779,789,790,791,792,793,794,795,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,855,879,880,901,902,921,922,935,],[-487,-486,-499,304,-485,404,-506,-507,-488,-499,-490,-491,-492,-493,790,-369,800,-394,-499,-499,-395,-396,-397,-398,-399,-400,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-393,-380,-499,-394,790,-381,-499,790,]),'+':([59,129,197,213,327,644,677,678,679,680,745,746,747,749,779,789,790,791,792,793,794,795,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,855,879,880,901,902,921,922,935,],[-487,-486,303,-485,-488,-499,-490,-491,-492,-493,791,-369,801,-394,-499,-499,-395,-396,-397,-398,-399,-400,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-393,-380,-499,-394,791,-381,-499,791,]),'|':([59,129,197,213,327,677,678,679,680,745,746,836,837,838,839,840,841,842,843,844,845,848,849,850,851,852,853,854,879,902,921,935,],[-487,-486,309,-485,-488,-490,-491,-492,-493,795,-369,-370,-371,-372,-373,-374,-375,-376,-377,-378,-379,-382,-383,-384,-385,-386,-387,-388,-380,795,-381,795,]),'FILE_PATH':([63,64,155,157,553,556,],[-51,-51,234,234,234,234,]),'ConvertFromTypeCode':([82,87,88,89,90,162,163,173,181,200,240,241,242,243,244,245,246,247,248,249,250,251,258,275,370,371,372,373,375,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,252,252,-196,-249,-195,252,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,252,252,-139,-67,-69,-125,-192,-194,-107,-251,252,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'ConvertToTypeCode':([82,87,88,89,90,162,163,173,181,200,240,241,242,243,244,245,246,247,248,249,250,251,258,275,370,371,372,373,375,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,253,253,-196,-249,-195,253,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,253,253,-139,-67,-69,-125,-192,-194,-107,-251,253,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'InstanceCode':([82,87,88,89,90,162,163,173,181,200,240,241,242,243,244,245,246,247,248,249,250,251,258,275,370,371,372,373,375,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,254,254,-196,-249,-195,254,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,254,254,-139,-67,-69,-125,-192,-194,-107,-251,254,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'static':([82,87,88,89,90,162,163,173,181,200,240,241,242,243,244,245,246,247,248,249,250,251,258,275,370,371,372,373,375,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,255,255,-196,-249,-195,255,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,255,471,-139,-67,-69,-125,-192,-194,-107,-251,471,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,471,-314,471,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'ReleaseCode':([82,162,163,200,240,241,242,243,244,245,246,247,248,249,250,251,258,370,371,372,373,375,376,402,652,732,733,830,938,941,949,952,953,960,964,969,],[-108,256,256,-195,256,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,256,-139,-67,-69,-125,-192,-194,-107,-81,-502,-503,-328,-499,-451,-499,-449,-450,-499,-448,-150,]),'TypeCode':([82,87,88,89,90,162,163,173,181,200,240,241,242,243,244,245,246,247,248,249,250,251,258,275,370,371,372,373,375,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,938,939,941,942,944,945,947,948,949,950,952,953,954,956,958,959,960,961,963,964,965,967,968,969,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,257,257,-196,-249,-195,257,-138,-140,-141,-142,-143,-144,-145,-146,-147,-148,-149,257,257,-139,-67,-69,-125,-192,-194,-107,-251,257,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-499,-311,-455,-448,-499,-499,-499,-150,-454,-499,-499,-360,-453,-357,-359,-452,]),'Docstring':([82,87,88,89,90,173,181,200,216,260,275,328,329,330,331,332,371,372,373,376,378,379,380,381,382,383,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,529,568,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,895,906,908,912,913,914,915,916,917,926,927,931,932,933,937,938,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,333,333,333,333,-62,-64,-65,-66,-67,-69,-125,-194,333,-167,-169,-170,-171,-172,-107,-251,333,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-63,-168,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,333,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,333,-52,333,333,-187,-189,-190,-191,333,-499,-499,-188,-463,333,-499,333,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'AutoPyName':([82,260,378,379,380,381,382,383,402,568,652,906,],[-108,384,384,-167,-169,-170,-171,-172,-107,-168,-81,-52,]),'BIGetReadBufferCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,443,-67,-69,-125,-194,-107,-251,443,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'BIGetWriteBufferCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,444,-67,-69,-125,-194,-107,-251,444,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'BIGetSegCountCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,445,-67,-69,-125,-194,-107,-251,445,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'BIGetCharBufferCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,446,-67,-69,-125,-194,-107,-251,446,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'explicit':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,449,-67,-69,-125,-194,-107,-251,449,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Q_SIGNAL':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,453,-67,-69,-125,-194,-107,-251,453,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Q_SLOT':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,455,-67,-69,-125,-194,-107,-251,455,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'public':([82,87,88,89,90,173,181,200,275,278,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,620,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,456,479,-67,-69,-125,-194,-107,-251,456,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,479,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'protected':([82,87,88,89,90,173,181,200,275,278,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,620,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,457,480,-67,-69,-125,-194,-107,-251,457,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,480,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'private':([82,87,88,89,90,173,181,200,275,278,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,620,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,458,481,-67,-69,-125,-194,-107,-251,458,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,481,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'signals':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,459,-67,-69,-125,-194,-107,-251,459,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Q_SIGNALS':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,460,-67,-69,-125,-194,-107,-251,460,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'ConvertToSubClassCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,461,-67,-69,-125,-194,-107,-251,461,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'FinalisationCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,462,-67,-69,-125,-194,-107,-251,462,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'GCClearCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,463,-67,-69,-125,-194,-107,-251,463,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'GCTraverseCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,464,-67,-69,-125,-194,-107,-251,464,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'BIGetBufferCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,465,-67,-69,-125,-194,-107,-251,465,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'PickleCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,466,-67,-69,-125,-194,-107,-251,466,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'Property':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,467,-67,-69,-125,-194,-107,-251,467,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'BIReleaseBufferCode':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,454,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,468,-67,-69,-125,-194,-107,-251,468,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,-314,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'virtual':([82,87,88,89,90,173,181,200,275,371,372,373,376,402,406,408,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,453,454,455,470,472,473,486,492,523,588,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,652,707,708,709,731,732,733,805,830,833,867,893,894,908,917,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,-195,469,-67,-69,-125,-194,-107,-251,469,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,599,-314,599,-318,-317,-319,-456,-253,-464,-269,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-81,-320,-321,-322,-462,-502,-503,-358,-328,-344,-499,-179,-185,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'~':([82,87,88,89,90,173,181,197,200,275,371,372,373,376,402,406,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,447,450,451,454,469,470,472,473,486,492,523,588,589,590,591,592,593,594,598,600,607,608,609,610,611,612,613,614,616,617,619,630,632,644,652,707,708,709,731,732,733,747,749,779,789,790,791,792,793,794,795,805,830,833,855,867,880,893,894,901,908,917,922,926,927,932,933,937,939,941,942,944,945,947,948,950,952,953,954,956,958,959,961,963,964,965,967,968,970,971,973,974,976,977,978,979,],[-108,-353,-354,-355,-356,-196,-249,313,-195,-499,-67,-69,-125,-194,-107,-251,-499,-509,-268,-270,-271,-272,-273,-274,-275,-276,-277,-278,-279,-280,-281,-282,-283,-284,-285,-286,-287,-288,-289,-290,-291,-292,-293,-294,-295,-296,-297,-298,-299,-300,-301,-250,-307,596,-314,-508,-318,-317,-319,-456,-253,-464,-269,-509,-302,-303,-304,-305,-306,-312,-313,-323,-324,-68,-99,-100,-101,-53,-174,-54,-315,-316,-499,-466,-499,-81,-320,-321,-322,-462,-502,-503,799,-394,-499,-499,-395,-396,-397,-398,-399,-400,-358,-328,-344,-393,-499,-499,-179,-185,-394,-499,-499,-499,-499,-499,-463,-499,-499,-499,-451,-499,-186,-499,-499,-499,-499,-449,-450,-308,-499,-499,-499,-311,-455,-448,-499,-499,-499,-454,-499,-499,-360,-453,-357,-359,-452,]),'AccessCode':([82,402,497,633,634,635,636,736,737,738,739,],[-108,-107,637,637,-469,-471,-472,-470,-473,-474,-475,]),'GetCode':([82,402,497,633,634,635,636,736,737,738,739,],[-108,-107,638,638,-469,-471,-472,-470,-473,-474,-475,]),'SetCode':([82,402,497,633,634,635,636,736,737,738,739,],[-108,-107,639,639,-469,-471,-472,-470,-473,-474,-475,]),'RaiseCode':([82,402,623,720,721,722,723,782,783,784,],[-108,-107,724,724,-347,-349,-350,-348,-351,-352,]),'!':([178,197,403,644,747,749,779,789,790,791,792,793,794,795,855,880,901,922,],[272,315,585,-499,798,-394,-499,-499,-395,-396,-397,-398,-399,-400,-393,-499,-394,-499,]),'%':([197,],[307,]),'^':([197,],[310,]),'[':([197,291,627,641,650,741,742,743,751,752,770,786,787,788,806,807,818,821,822,823,835,856,857,862,865,876,878,881,882,883,897,898,903,904,905,910,923,],[314,-477,-476,-499,-499,-499,-500,-501,-499,-499,-499,-499,-504,-505,-499,-499,-499,-499,-408,-409,-499,-499,-499,-499,887,-499,-416,-499,-499,-499,919,-415,-499,919,919,-410,919,]),'name':([218,219,220,221,222,223,226,227,235,238,262,267,535,548,552,555,571,582,710,826,],[336,337,338,339,340,341,346,349,356,361,392,401,336,349,356,361,392,401,774,774,]),'id':([225,543,],[344,344,]),'order':([225,543,],[345,345,]),'EOL':([230,231,232,233,234,236,237,],[352,-111,-496,-497,-498,357,-118,]),'optional':([238,555,],[362,362,]),'licensee':([239,559,],[365,365,]),'signature':([239,559,653,809,],[366,366,756,756,]),'timestamp':([239,559,],[367,367,]),'type':([239,559,],[368,368,]),'all_raise_py_exception':([262,571,],[387,387,]),'call_super_init':([262,571,],[388,388,]),'default_VirtualErrorHandler':([262,571,],[389,389,]),'keyword_arguments':([262,571,],[390,390,]),'language':([262,571,],[391,391,]),'py_ssize_t_clean':([262,571,],[393,393,]),'use_argument_names':([262,571,],[394,394,]),'use_limited_api':([262,571,],[395,395,]),'LOGICAL_OR':([269,271,405,584,704,],[403,-510,-511,-512,-513,]),']':([314,943,966,],[521,955,972,]),'slots':([456,457,458,],[602,602,602,]),'Q_SLOTS':([456,457,458,],[603,603,603,]),'NUMBER':([545,629,644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,877,880,901,922,],[665,730,-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,849,-401,-402,-403,-404,-405,-406,-407,-393,898,-499,-394,-499,]),'true':([557,572,573,578,579,580,644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[677,677,677,677,677,677,-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,677,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'True':([557,572,573,578,579,580,644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[678,678,678,678,678,678,-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,678,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'false':([557,572,573,578,579,580,644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[679,679,679,679,679,679,-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,679,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'False':([557,572,573,578,579,580,644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[680,680,680,680,680,680,-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,680,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'final':([641,650,741,742,743,751,752,806,],[-499,-499,787,-500,-501,-499,787,787,]),'noexcept':([641,650,741,742,743,751,752,770,786,787,788,806,807,818,820,856,862,],[-499,-499,-499,-500,-501,-499,-499,823,823,-504,-505,-499,823,-499,823,823,823,]),'throw':([641,650,741,742,743,751,752,770,786,787,788,806,807,818,820,856,862,],[-499,-499,-499,-500,-501,-499,-499,824,824,-504,-505,-499,824,-499,824,824,824,]),'NULL':([644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,848,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'QUOTED_CHAR':([644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,851,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'REAL':([644,747,749,779,789,790,791,792,793,794,795,796,797,798,799,800,801,802,803,855,880,901,922,],[-499,-499,-394,-499,-499,-395,-396,-397,-398,-399,-400,852,-401,-402,-403,-404,-405,-406,-407,-393,-499,-394,-499,]),'PreMethodCode':([652,732,733,908,926,927,933,937,938,945,947,949,],[-81,-502,-503,-499,940,940,-499,940,-499,940,940,940,]),'MethodCode':([652,732,733,908,926,927,933,937,938,939,941,942,945,947,948,949,953,956,958,960,],[-81,-502,-503,-499,-499,-499,-499,-499,-499,951,-451,951,-499,-499,951,-499,-450,951,951,951,]),'VirtualCatcherCode':([652,732,733,926,933,937,939,941,945,947,948,950,952,953,956,958,959,964,965,967,],[-81,-502,-503,-499,-499,-499,-499,-451,-499,-499,-499,962,-449,-450,-499,-499,962,-448,962,962,]),'VirtualCallCode':([652,732,733,933,937,941,945,947,948,952,953,956,958,959,963,964,965,967,968,970,971,973,],[-81,-502,-503,-499,-499,-451,-499,-499,-499,-449,-450,-499,-499,-499,-455,-448,-499,-499,975,-454,975,975,]),'format':([653,809,],[755,755,]),'remove_leading':([689,],[766,]),'get':([710,826,],[773,773,]),'set':([710,826,],[775,775,]),}

_lr_action = {}
for _k, _v in _lr_action_items.items():
   for _x,_y in zip(_v[0],_v[1]):
      if not _x in _lr_action:  _lr_action[_x] = {}
      _lr_action[_x][_k] = _y
del _lr_action_items

_lr_goto_items = {'specification':([0,],[1,]),'statement':([0,1,],[2,130,]),'eof':([0,1,],[3,3,]),'namespace_statement':([0,1,287,487,],[4,4,488,626,]),'composite_module':([0,1,],[5,5,]),'copying':([0,1,],[6,6,]),'defdocstringfmt':([0,1,],[7,7,]),'defdocstringsig':([0,1,],[8,8,]),'defencoding':([0,1,],[9,9,]),'defmetatype':([0,1,],[10,10,]),'defsupertype':([0,1,],[11,11,]),'exported_header_code':([0,1,],[12,12,]),'exported_type_hint_code':([0,1,],[13,13,]),'extract':([0,1,],[14,14,]),'feature':([0,1,],[15,15,]),'hidden_ns':([0,1,],[16,16,]),'import':([0,1,],[17,17,]),'include':([0,1,],[18,18,]),'init_code':([0,1,],[19,19,]),'license':([0,1,],[20,20,]),'mapped_type':([0,1,],[21,21,]),'mapped_type_template':([0,1,],[22,22,]),'module':([0,1,],[23,23,]),'module_code':([0,1,],[24,24,]),'module_header_code':([0,1,],[25,25,]),'platforms':([0,1,],[26,26,]),'plugin':([0,1,],[27,27,]),'preinit_code':([0,1,],[28,28,]),'postinit_code':([0,1,],[29,29,]),'timeline':([0,1,],[30,30,]),'type_hint_code':([0,1,275,408,],[31,31,442,442,]),'unit_code':([0,1,],[32,32,]),'unit_postinclude_code':([0,1,],[33,33,]),'virtual_error_handler':([0,1,],[34,34,]),'if_start':([0,1,162,163,216,240,258,260,275,287,328,378,408,487,497,622,623,633,714,720,895,912,],[36,36,242,242,330,242,242,380,411,36,330,380,411,36,635,717,722,635,717,722,914,914,]),'if_end':([0,1,162,163,216,240,258,260,275,287,328,378,408,487,497,622,623,633,714,720,895,912,],[37,37,243,243,331,243,243,381,412,37,331,381,412,37,636,718,723,636,718,723,915,915,]),'class_decl':([0,1,84,275,287,408,448,487,],[38,38,181,413,38,413,181,38,]),'class_template':([0,1,275,287,408,487,],[39,39,415,39,415,39,]),'enum_decl':([0,1,162,163,240,258,275,287,408,487,],[40,40,246,246,246,246,418,40,418,40,]),'exception':([0,1,275,287,408,487,],[41,41,419,41,419,41,]),'function':([0,1,275,287,408,453,455,469,471,487,599,],[42,42,470,42,470,470,470,617,470,42,617,]),'namespace_decl':([0,1,275,287,408,487,],[43,43,422,43,422,43,]),'struct_decl':([0,1,275,287,408,487,],[44,44,423,44,423,44,]),'typedef_decl':([0,1,275,287,408,487,],[45,45,420,45,420,45,]),'union_decl':([0,1,275,287,408,487,],[46,46,424,46,424,46,]),'variable':([0,1,275,287,408,453,455,471,487,],[47,47,473,47,473,473,473,473,47,]),'type_header_code':([0,1,162,163,240,258,275,287,408,487,],[48,48,251,251,251,251,441,48,441,48,]),'scoped_name':([0,1,61,83,86,91,92,93,95,97,100,101,154,182,194,195,204,255,275,284,287,299,324,351,408,453,455,469,471,477,487,506,508,549,597,599,642,687,748,785,796,866,909,911,919,946,],[62,62,152,180,188,190,192,62,199,62,62,62,62,62,296,297,62,62,62,485,62,62,62,62,62,62,62,62,62,621,62,62,62,669,62,62,62,62,804,62,853,892,62,929,62,62,]),'mapped_type_head':([0,1,],[67,67,]),'mapped_type_template_head':([0,1,],[68,68,]),'template_decl':([0,1,275,287,408,487,],[84,84,448,448,448,448,]),'function_decl':([0,1,275,287,408,453,455,469,471,487,599,],[87,87,87,87,87,87,87,87,87,87,87,]),'assignment_operator_decl':([0,1,275,287,408,453,455,469,471,487,599,],[88,88,88,88,88,88,88,88,88,88,88,]),'operator_decl':([0,1,275,287,408,453,455,469,471,487,599,],[89,89,89,89,89,89,89,89,89,89,89,]),'operator_cast_decl':([0,1,275,287,408,453,455,469,471,487,599,],[90,90,90,90,90,90,90,90,90,90,90,]),'cpp_type':([0,1,93,100,154,204,255,275,287,299,324,351,408,453,455,469,471,487,506,508,597,599,642,687,785,909,919,946,],[94,94,193,205,229,229,374,94,94,499,499,550,94,94,94,618,94,94,646,499,499,618,499,499,229,499,934,499,]),'base_type':([0,1,93,97,100,101,154,182,204,255,275,287,299,324,351,408,453,455,469,471,487,506,508,597,599,642,687,785,796,909,919,946,],[98,98,98,201,98,206,98,280,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,98,847,98,98,98,]),'pod_type':([0,1,93,97,100,101,154,182,204,255,275,287,299,324,351,408,453,455,469,471,487,506,508,597,599,642,687,785,796,909,919,946,],[102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,102,]),'relative_scoped_name':([0,1,61,83,86,91,92,93,95,97,100,101,128,154,182,194,195,204,255,275,284,287,299,324,351,408,453,455,469,471,477,487,506,508,549,597,599,642,687,748,785,796,866,909,911,919,946,],[129,129,129,129,129,129,129,129,129,129,129,129,213,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,129,]),'dotted_name':([49,54,55,69,536,540,541,577,629,],[131,142,144,164,656,660,661,697,728,]),'begin_args':([49,51,52,53,54,55,58,60,61,63,64,66,69,80,333,384,467,],[132,137,139,141,143,145,149,151,153,156,158,161,165,177,533,569,615,]),'need_eol':([63,64,],[155,157,]),'class_head':([83,],[179,]),'opt_enum_key':([85,],[183,]),'empty':([85,98,131,164,178,179,180,183,188,189,190,191,192,196,198,199,201,202,206,275,277,278,280,281,283,293,294,298,299,324,325,333,404,408,456,457,458,499,504,508,597,620,622,630,640,641,644,650,654,687,690,719,741,747,751,752,770,778,779,786,789,806,807,818,820,821,831,835,856,857,862,864,865,866,867,875,876,880,881,882,883,885,897,903,904,905,908,909,917,922,923,926,927,933,937,938,939,942,945,946,947,948,949,950,956,958,959,960,965,967,968,971,973,],[187,203,217,261,273,276,279,273,285,288,291,276,279,291,276,291,291,322,203,409,291,478,291,291,291,291,291,498,502,502,322,531,273,589,604,604,604,273,645,502,502,478,715,733,291,743,749,743,217,502,261,780,788,797,743,788,822,291,749,822,749,788,822,743,822,291,873,878,822,878,822,878,888,891,894,291,291,901,878,291,291,291,920,291,920,920,733,502,733,749,920,941,941,733,941,733,952,952,941,502,941,952,941,963,952,952,963,952,963,963,976,976,976,]),'namespace_head':([91,],[189,]),'struct_head':([92,],[191,]),'union_head':([95,],[198,]),'derefs':([98,206,],[202,325,]),'c_module_body':([131,654,],[215,757,]),'cpp_types':([154,204,785,],[228,323,834,]),'import_simple':([155,],[230,]),'file_path':([155,157,553,556,],[231,237,672,675,]),'include_simple':([157,],[236,]),'mapped_type_body':([162,163,],[240,258,]),'mapped_type_line':([162,163,240,258,],[241,241,370,370,]),'convert_from_type_code':([162,163,240,258,275,408,],[244,244,244,244,429,429,]),'convert_to_type_code':([162,163,240,258,275,408,],[245,245,245,245,431,431,]),'instance_code':([162,163,240,258,275,408,],[247,247,247,247,436,436,]),'mapped_type_function':([162,163,240,258,],[248,248,248,248,]),'release_code':([162,163,240,258,],[249,249,249,249,]),'type_code':([162,163,240,258,275,408,],[250,250,250,250,440,440,]),'module_body':([164,690,],[259,767,]),'qualifier_list':([168,172,],[263,265,]),'qualifiers':([178,],[268,]),'ored_qualifiers':([178,],[269,]),'opt_name':([178,183,404,499,],[270,281,586,640,]),'opt_class_definition':([179,191,198,],[274,292,316,]),'superclasses':([180,192,],[277,293,]),'opt_base_exception':([188,],[283,]),'opt_namespace_body':([189,],[286,]),'opt_annos':([190,196,199,201,277,280,281,283,293,294,640,778,821,875,876,882,883,885,903,],[289,298,317,318,474,482,483,484,493,494,740,831,865,896,897,904,905,907,923,]),'operator_name':([197,],[302,]),'opt_ref':([202,325,],[319,527,]),'c_module_body_directives':([216,],[328,]),'c_module_body_directive':([216,328,],[329,529,]),'docstring':([216,260,275,328,378,408,630,895,908,912,917,933,938,],[332,383,447,332,383,447,732,916,732,916,732,732,732,]),'c_module_args':([218,],[334,]),'c_module_arg':([218,535,],[335,655,]),'extract_args':([225,],[342,]),'extract_arg':([225,543,],[343,663,]),'hidden_ns_args':([227,],[347,]),'hidden_ns_arg':([227,548,],[348,668,]),'import_compound':([235,],[353,]),'import_args':([235,],[354,]),'import_arg':([235,552,],[355,671,]),'include_compound':([238,],[358,]),'include_args':([238,],[359,]),'include_arg':([238,555,],[360,674,]),'license_args':([239,],[363,]),'license_arg':([239,559,],[364,682,]),'module_body_directives':([260,],[378,]),'module_body_directive':([260,378,],[379,568,]),'autopyname':([260,378,],[382,382,]),'module_args':([262,],[385,]),'module_arg':([262,571,],[386,691,]),'veh_args':([267,],[399,]),'veh_arg':([267,582,],[400,702,]),'opt_class_body':([275,],[407,]),'class_body':([275,],[408,]),'class_line':([275,408,],[410,588,]),'class_docstring':([275,408,],[414,414,]),'ctor':([275,408,],[416,416,]),'dtor':([275,408,],[417,417,]),'method_variable':([275,408,],[421,421,]),'public_specifier':([275,408,],[425,425,]),'protected_specifier':([275,408,],[426,426,]),'private_specifier':([275,408,],[427,427,]),'signals_specifier':([275,408,],[428,428,]),'convert_to_subclass_code':([275,408,],[430,430,]),'finalisation_code':([275,408,],[432,432,]),'gc_clear_code':([275,408,],[433,433,]),'gc_traverse_code':([275,408,],[434,434,]),'get_buffer_code':([275,408,],[435,435,]),'pickle_code':([275,408,],[437,437,]),'property':([275,408,],[438,438,]),'release_buffer_code':([275,408,],[439,439,]),'ctor_decl':([275,408,449,],[450,450,594,]),'opt_virtual':([275,408,],[451,451,]),'simple_method_variable':([275,408,453,455,],[454,454,598,600,]),'plain_method_variable':([275,408,453,455,471,],[472,472,472,472,619,]),'superclass_list':([278,],[475,]),'superclass':([278,620,],[476,712,]),'class_access':([278,620,],[477,477,]),'namespace_body':([287,],[487,]),'annotations':([290,],[489,]),'annotation':([290,628,],[490,726,]),'variable_body':([298,],[496,]),'opt_arg_list':([299,324,508,597,687,909,946,],[500,526,647,706,765,928,957,]),'arg_list':([299,324,508,597,687,909,946,],[501,501,501,501,501,501,501,]),'arg_value':([299,324,508,597,642,687,909,946,],[503,503,503,503,744,503,503,503,]),'arg_type':([299,324,508,597,642,687,909,946,],[504,504,504,504,504,504,504,504,]),'docstring_args':([333,],[530,]),'end_args':([334,342,347,353,358,363,385,399,657,658,659,660,661,666,753,771,863,],[534,542,547,551,554,558,570,581,758,759,760,761,762,764,808,825,884,]),'opt_slots':([456,457,458,],[601,605,606,]),'variable_body_directives':([497,],[633,]),'variable_body_directive':([497,633,],[634,736,]),'opt_assign':([504,],[643,]),'bool_value':([557,572,573,578,579,580,796,],[676,692,693,698,699,700,850,]),'opt_enum_body':([622,],[713,]),'enum_body':([622,],[714,]),'enum_line':([622,714,],[716,777,]),'exception_body':([623,],[720,]),'exception_line':([623,720,],[721,782,]),'annotation_value':([629,],[727,]),'opt_docstring':([630,908,917,933,938,],[731,927,932,945,949,]),'opt_const':([641,650,751,818,],[741,752,806,862,]),'expr':([644,880,922,],[745,902,935,]),'value':([644,779,789,880,922,],[746,832,836,746,746,]),'opt_cast':([644,779,789,880,922,],[747,747,747,747,747,]),'docstring_arg_list':([653,],[753,]),'docstring_arg':([653,809,],[754,859,]),'property_args':([710,],[771,]),'property_arg':([710,826,],[772,868,]),'opt_enum_assign':([719,],[778,]),'opt_final':([741,752,806,],[786,807,856,]),'binop':([745,902,935,],[789,789,789,]),'opt_unop':([747,],[796,]),'opt_exceptions':([770,786,807,820,856,862,],[821,835,857,864,881,883,]),'simple_value':([796,],[837,]),'empty_value':([796,],[838,]),'function_call_value':([796,],[839,]),'null_value':([796,],[840,]),'number_value':([796,],[841,]),'quoted_char_value':([796,],[842,]),'real_value':([796,],[843,]),'scoped_name_value':([796,],[844,]),'string_value':([796,],[845,]),'opt_comma':([831,],[872,]),'opt_abstract':([835,857,864,881,],[876,882,885,903,]),'opt_ctor_signature':([865,],[886,]),'opt_exception_list':([866,],[889,]),'exception_list':([866,],[890,]),'opt_property_body':([867,],[893,]),'opt_expr_list':([880,],[899,]),'expr_list':([880,],[900,]),'property_body':([895,],[912,]),'property_line':([895,912,],[913,931,]),'opt_signature':([897,904,905,923,],[918,924,925,936,]),'premethod_code':([926,927,937,945,947,949,],[939,942,948,956,958,960,]),'method_code':([939,942,948,956,958,960,],[950,954,959,965,967,969,]),'virtual_catcher_code':([950,959,965,967,],[961,968,971,973,]),'virtual_call_code':([968,971,973,],[974,977,978,]),}

_lr_goto = {}
for _k, _v in _lr_goto_items.items():
   for _x, _y in zip(_v[0], _v[1]):
       if not _x in _lr_goto: _lr_goto[_x] = {}
       _lr_goto[_x][_k] = _y
del _lr_goto_items
_lr_productions = [
  ("S' -> specification","S'",1,None,None,None),
  ('specification -> statement','specification',1,'p_specification','rules.py',67),
  ('specification -> specification statement','specification',2,'p_specification','rules.py',68),
  ('statement -> eof','statement',1,'p_statement','rules.py',72),
  ('statement -> namespace_statement','statement',1,'p_statement','rules.py',73),
  ('statement -> composite_module','statement',1,'p_statement','rules.py',74),
  ('statement -> copying','statement',1,'p_statement','rules.py',75),
  ('statement -> defdocstringfmt','statement',1,'p_statement','rules.py',76),
  ('statement -> defdocstringsig','statement',1,'p_statement','rules.py',77),
  ('statement -> defencoding','statement',1,'p_statement','rules.py',78),
  ('statement -> defmetatype','statement',1,'p_statement','rules.py',79),
  ('statement -> defsupertype','statement',1,'p_statement','rules.py',80),
  ('statement -> exported_header_code','statement',1,'p_statement','rules.py',81),
  ('statement -> exported_type_hint_code','statement',1,'p_statement','rules.py',82),
  ('statement -> extract','statement',1,'p_statement','rules.py',83),
  ('statement -> feature','statement',1,'p_statement','rules.py',84),
  ('statement -> hidden_ns','statement',1,'p_statement','rules.py',85),
  ('statement -> import','statement',1,'p_statement','rules.py',86),
  ('statement -> include','statement',1,'p_statement','rules.py',87),
  ('statement -> init_code','statement',1,'p_statement','rules.py',88),
  ('statement -> license','statement',1,'p_statement','rules.py',89),
  ('statement -> mapped_type','statement',1,'p_statement','rules.py',90),
  ('statement -> mapped_type_template','statement',1,'p_statement','rules.py',91),
  ('statement -> module','statement',1,'p_statement','rules.py',92),
  ('statement -> module_code','statement',1,'p_statement','rules.py',93),
  ('statement -> module_header_code','statement',1,'p_statement','rules.py',94),
  ('statement -> platforms','statement',1,'p_statement','rules.py',95),
  ('statement -> plugin','statement',1,'p_statement','rules.py',96),
  ('statement -> preinit_code','statement',1,'p_statement','rules.py',97),
  ('statement -> postinit_code','statement',1,'p_statement','rules.py',98),
  ('statement -> timeline','statement',1,'p_statement','rules.py',99),
  ('statement -> type_hint_code','statement',1,'p_statement','rules.py',100),
  ('statement -> unit_code','statement',1,'p_statement','rules.py',101),
  ('statement -> unit_postinclude_code','statement',1,'p_statement','rules.py',102),
  ('statement -> virtual_error_handler','statement',1,'p_statement','rules.py',103),
  ('namespace_statement -> if_start','namespace_statement',1,'p_namespace_statement','rules.py',107),
  ('namespace_statement -> if_end','namespace_statement',1,'p_namespace_statement','rules.py',108),
  ('namespace_statement -> class_decl','namespace_statement',1,'p_namespace_statement','rules.py',109),
  ('namespace_statement -> class_template','namespace_statement',1,'p_namespace_statement','rules.py',110),
  ('namespace_statement -> enum_decl','namespace_statement',1,'p_namespace_statement','rules.py',111),
  ('namespace_statement -> exception','namespace_statement',1,'p_namespace_statement','rules.py',112),
  ('namespace_statement -> function','namespace_statement',1,'p_namespace_statement','rules.py',113),
  ('namespace_statement -> namespace_decl','namespace_statement',1,'p_namespace_statement','rules.py',114),
  ('namespace_statement -> struct_decl','namespace_statement',1,'p_namespace_statement','rules.py',115),
  ('namespace_statement -> typedef_decl','namespace_statement',1,'p_namespace_statement','rules.py',116),
  ('namespace_statement -> union_decl','namespace_statement',1,'p_namespace_statement','rules.py',117),
  ('namespace_statement -> variable','namespace_statement',1,'p_namespace_statement','rules.py',118),
  ('namespace_statement -> type_header_code','namespace_statement',1,'p_namespace_statement','rules.py',119),
  ('eof -> EOF','eof',1,'p_eof','rules.py',123),
  ('begin_args -> <empty>','begin_args',0,'p_begin_args','rules.py',131),
  ('end_args -> <empty>','end_args',0,'p_end_args','rules.py',137),
  ('need_eol -> <empty>','need_eol',0,'p_need_eol','rules.py',143),
  ('autopyname -> AutoPyName begin_args ( remove_leading = STRING end_args )','autopyname',8,'p_autopyname','rules.py',151),
  ('get_buffer_code -> BIGetBufferCode CODE_BLOCK','get_buffer_code',2,'p_get_buffer_code','rules.py',162),
  ('release_buffer_code -> BIReleaseBufferCode CODE_BLOCK','release_buffer_code',2,'p_release_buffer_code','rules.py',178),
  ('composite_module -> CompositeModule dotted_name c_module_body','composite_module',3,'p_composite_module','rules.py',195),
  ('composite_module -> CompositeModule begin_args ( c_module_args end_args ) c_module_body','composite_module',7,'p_composite_module','rules.py',196),
  ('c_module_args -> c_module_arg','c_module_args',1,'p_c_module_args','rules.py',228),
  ('c_module_args -> c_module_args , c_module_arg','c_module_args',3,'p_c_module_args','rules.py',229),
  ('c_module_arg -> name = dotted_name','c_module_arg',3,'p_c_module_arg','rules.py',238),
  ('c_module_body -> { c_module_body_directives } ;','c_module_body',4,'p_c_module_body','rules.py',244),
  ('c_module_body -> empty','c_module_body',1,'p_c_module_body','rules.py',245),
  ('c_module_body_directives -> c_module_body_directive','c_module_body_directives',1,'p_c_module_body_directives','rules.py',251),
  ('c_module_body_directives -> c_module_body_directives c_module_body_directive','c_module_body_directives',2,'p_c_module_body_directives','rules.py',252),
  ('c_module_body_directive -> if_start','c_module_body_directive',1,'p_c_module_body_directive','rules.py',268),
  ('c_module_body_directive -> if_end','c_module_body_directive',1,'p_c_module_body_directive','rules.py',269),
  ('c_module_body_directive -> docstring','c_module_body_directive',1,'p_c_module_body_directive','rules.py',270),
  ('convert_from_type_code -> ConvertFromTypeCode CODE_BLOCK','convert_from_type_code',2,'p_convert_from_type_code','rules.py',278),
  ('convert_to_subclass_code -> ConvertToSubClassCode CODE_BLOCK','convert_to_subclass_code',2,'p_convert_to_subclass_code','rules.py',295),
  ('convert_to_type_code -> ConvertToTypeCode CODE_BLOCK','convert_to_type_code',2,'p_convert_to_type_code','rules.py',312),
  ('copying -> Copying CODE_BLOCK','copying',2,'p_copying','rules.py',328),
  ('defdocstringfmt -> DefaultDocstringFormat STRING','defdocstringfmt',2,'p_defdocstringfmt','rules.py',341),
  ('defdocstringfmt -> DefaultDocstringFormat begin_args ( name = STRING end_args )','defdocstringfmt',8,'p_defdocstringfmt','rules.py',342),
  ('defdocstringsig -> DefaultDocstringSignature STRING','defdocstringsig',2,'p_defdocstringsig','rules.py',357),
  ('defdocstringsig -> DefaultDocstringSignature begin_args ( name = STRING end_args )','defdocstringsig',8,'p_defdocstringsig','rules.py',358),
  ('defencoding -> DefaultEncoding STRING','defencoding',2,'p_defencoding','rules.py',373),
  ('defencoding -> DefaultEncoding begin_args ( name = STRING end_args )','defencoding',8,'p_defencoding','rules.py',374),
  ('defmetatype -> DefaultMetatype dotted_name','defmetatype',2,'p_defmetatype','rules.py',389),
  ('defmetatype -> DefaultMetatype begin_args ( name = dotted_name end_args )','defmetatype',8,'p_defmetatype','rules.py',390),
  ('defsupertype -> DefaultSupertype dotted_name','defsupertype',2,'p_defsupertype','rules.py',411),
  ('defsupertype -> DefaultSupertype begin_args ( name = dotted_name end_args )','defsupertype',8,'p_defsupertype','rules.py',412),
  ('docstring -> Docstring docstring_args CODE_BLOCK','docstring',3,'p_docstring','rules.py',433),
  ('docstring_args -> empty','docstring_args',1,'p_docstring_args','rules.py',456),
  ('docstring_args -> STRING','docstring_args',1,'p_docstring_args','rules.py',457),
  ('docstring_args -> begin_args ( docstring_arg_list end_args )','docstring_args',5,'p_docstring_args','rules.py',458),
  ('docstring_arg_list -> docstring_arg','docstring_arg_list',1,'p_docstring_arg_list','rules.py',474),
  ('docstring_arg_list -> docstring_arg_list , docstring_arg','docstring_arg_list',3,'p_docstring_arg_list','rules.py',475),
  ('docstring_arg -> format = STRING','docstring_arg',3,'p_docstring_arg','rules.py',484),
  ('docstring_arg -> signature = STRING','docstring_arg',3,'p_docstring_arg','rules.py',485),
  ('exported_header_code -> ExportedHeaderCode CODE_BLOCK','exported_header_code',2,'p_exported_header_code','rules.py',500),
  ('exported_type_hint_code -> ExportedTypeHintCode CODE_BLOCK','exported_type_hint_code',2,'p_exported_type_hint_code','rules.py',513),
  ('extract -> Extract NAME CODE_BLOCK','extract',3,'p_extract','rules.py',527),
  ('extract -> Extract begin_args ( extract_args end_args ) CODE_BLOCK','extract',7,'p_extract','rules.py',528),
  ('extract_args -> extract_arg','extract_args',1,'p_extract_args','rules.py',555),
  ('extract_args -> extract_args , extract_arg','extract_args',3,'p_extract_args','rules.py',556),
  ('extract_arg -> id = NAME','extract_arg',3,'p_extract_arg','rules.py',565),
  ('extract_arg -> order = NUMBER','extract_arg',3,'p_extract_arg','rules.py',566),
  ('feature -> Feature NAME','feature',2,'p_feature','rules.py',574),
  ('feature -> Feature begin_args ( name = NAME end_args )','feature',8,'p_feature','rules.py',575),
  ('finalisation_code -> FinalisationCode CODE_BLOCK','finalisation_code',2,'p_finalisation_code','rules.py',586),
  ('gc_clear_code -> GCClearCode CODE_BLOCK','gc_clear_code',2,'p_gc_clear_code','rules.py',602),
  ('gc_traverse_code -> GCTraverseCode CODE_BLOCK','gc_traverse_code',2,'p_gc_traverse_code','rules.py',618),
  ('hidden_ns -> HideNamespace scoped_name','hidden_ns',2,'p_hidden_ns','rules.py',634),
  ('hidden_ns -> HideNamespace begin_args ( hidden_ns_args end_args )','hidden_ns',6,'p_hidden_ns','rules.py',635),
  ('hidden_ns_args -> hidden_ns_arg','hidden_ns_args',1,'p_hidden_ns_args','rules.py',658),
  ('hidden_ns_args -> hidden_ns_args , hidden_ns_arg','hidden_ns_args',3,'p_hidden_ns_args','rules.py',659),
  ('hidden_ns_arg -> name = scoped_name','hidden_ns_arg',3,'p_hidden_ns_arg','rules.py',668),
  ('if_start -> If ( qualifiers )','if_start',4,'p_if_start','rules.py',676),
  ('if_end -> End','if_end',1,'p_if_end','rules.py',689),
  ('import -> Import need_eol import_simple EOL','import',4,'p_import','rules.py',702),
  ('import -> Import begin_args ( import_compound end_args )','import',6,'p_import','rules.py',703),
  ('import_simple -> file_path','import_simple',1,'p_import_simple','rules.py',707),
  ('import_compound -> import_args','import_compound',1,'p_import_compound','rules.py',718),
  ('import_args -> import_arg','import_args',1,'p_import_args','rules.py',734),
  ('import_args -> import_args , import_arg','import_args',3,'p_import_args','rules.py',735),
  ('import_arg -> name = file_path','import_arg',3,'p_import_arg','rules.py',744),
  ('include -> Include need_eol include_simple EOL','include',4,'p_include','rules.py',752),
  ('include -> Include begin_args ( include_compound end_args )','include',6,'p_include','rules.py',753),
  ('include_simple -> file_path','include_simple',1,'p_include_simple','rules.py',757),
  ('include_compound -> include_args','include_compound',1,'p_include_compund','rules.py',768),
  ('include_args -> include_arg','include_args',1,'p_include_args','rules.py',784),
  ('include_args -> include_args , include_arg','include_args',3,'p_include_args','rules.py',785),
  ('include_arg -> name = file_path','include_arg',3,'p_include_arg','rules.py',794),
  ('include_arg -> optional = bool_value','include_arg',3,'p_include_arg','rules.py',795),
  ('init_code -> InitialisationCode CODE_BLOCK','init_code',2,'p_init_code','rules.py',803),
  ('instance_code -> InstanceCode CODE_BLOCK','instance_code',2,'p_instance_code','rules.py',816),
  ('license -> License STRING','license',2,'p_license','rules.py',832),
  ('license -> License begin_args ( license_args end_args )','license',6,'p_license','rules.py',833),
  ('license_args -> license_arg','license_args',1,'p_license_args','rules.py',864),
  ('license_args -> license_args , license_arg','license_args',3,'p_license_args','rules.py',865),
  ('license_arg -> licensee = STRING','license_arg',3,'p_license_arg','rules.py',874),
  ('license_arg -> signature = STRING','license_arg',3,'p_license_arg','rules.py',875),
  ('license_arg -> timestamp = STRING','license_arg',3,'p_license_arg','rules.py',876),
  ('license_arg -> type = STRING','license_arg',3,'p_license_arg','rules.py',877),
  ('mapped_type -> mapped_type_head { mapped_type_body } ;','mapped_type',5,'p_mapped_type','rules.py',898),
  ('mapped_type_template -> mapped_type_template_head { mapped_type_body } ;','mapped_type_template',5,'p_mapped_type_template','rules.py',911),
  ('mapped_type_head -> MappedType base_type opt_annos','mapped_type_head',3,'p_mapped_type_head','rules.py',926),
  ('mapped_type_template_head -> template_decl MappedType base_type opt_annos','mapped_type_template_head',4,'p_mapped_type_template_head','rules.py',939),
  ('mapped_type_body -> mapped_type_line','mapped_type_body',1,'p_mapped_type_body','rules.py',973),
  ('mapped_type_body -> mapped_type_body mapped_type_line','mapped_type_body',2,'p_mapped_type_body','rules.py',974),
  ('mapped_type_line -> if_start','mapped_type_line',1,'p_mapped_type_line','rules.py',978),
  ('mapped_type_line -> if_end','mapped_type_line',1,'p_mapped_type_line','rules.py',979),
  ('mapped_type_line -> convert_from_type_code','mapped_type_line',1,'p_mapped_type_line','rules.py',980),
  ('mapped_type_line -> convert_to_type_code','mapped_type_line',1,'p_mapped_type_line','rules.py',981),
  ('mapped_type_line -> enum_decl','mapped_type_line',1,'p_mapped_type_line','rules.py',982),
  ('mapped_type_line -> instance_code','mapped_type_line',1,'p_mapped_type_line','rules.py',983),
  ('mapped_type_line -> mapped_type_function','mapped_type_line',1,'p_mapped_type_line','rules.py',984),
  ('mapped_type_line -> release_code','mapped_type_line',1,'p_mapped_type_line','rules.py',985),
  ('mapped_type_line -> type_code','mapped_type_line',1,'p_mapped_type_line','rules.py',986),
  ('mapped_type_line -> type_header_code','mapped_type_line',1,'p_mapped_type_line','rules.py',987),
  ('mapped_type_function -> static cpp_type NAME ( opt_arg_list ) opt_const opt_exceptions opt_annos opt_signature ; opt_docstring premethod_code method_code','mapped_type_function',14,'p_mapped_type_function','rules.py',991),
  ('module_header_code -> ModuleHeaderCode CODE_BLOCK','module_header_code',2,'p_module_header_code','rules.py',1013),
  ('module -> Module dotted_name module_body','module',3,'p_module','rules.py',1026),
  ('module -> Module begin_args ( module_args end_args ) module_body','module',7,'p_module','rules.py',1027),
  ('module_args -> module_arg','module_args',1,'p_module_args','rules.py',1102),
  ('module_args -> module_args , module_arg','module_args',3,'p_module_args','rules.py',1103),
  ('module_arg -> all_raise_py_exception = bool_value','module_arg',3,'p_module_arg','rules.py',1112),
  ('module_arg -> call_super_init = bool_value','module_arg',3,'p_module_arg','rules.py',1113),
  ('module_arg -> default_VirtualErrorHandler = NAME','module_arg',3,'p_module_arg','rules.py',1114),
  ('module_arg -> keyword_arguments = STRING','module_arg',3,'p_module_arg','rules.py',1115),
  ('module_arg -> language = STRING','module_arg',3,'p_module_arg','rules.py',1116),
  ('module_arg -> name = dotted_name','module_arg',3,'p_module_arg','rules.py',1117),
  ('module_arg -> py_ssize_t_clean = bool_value','module_arg',3,'p_module_arg','rules.py',1118),
  ('module_arg -> use_argument_names = bool_value','module_arg',3,'p_module_arg','rules.py',1119),
  ('module_arg -> use_limited_api = bool_value','module_arg',3,'p_module_arg','rules.py',1120),
  ('module_body -> { module_body_directives } ;','module_body',4,'p_module_body','rules.py',1141),
  ('module_body -> empty','module_body',1,'p_module_body','rules.py',1142),
  ('module_body_directives -> module_body_directive','module_body_directives',1,'p_module_body_directives','rules.py',1148),
  ('module_body_directives -> module_body_directives module_body_directive','module_body_directives',2,'p_module_body_directives','rules.py',1149),
  ('module_body_directive -> if_start','module_body_directive',1,'p_module_body_directive','rules.py',1165),
  ('module_body_directive -> if_end','module_body_directive',1,'p_module_body_directive','rules.py',1166),
  ('module_body_directive -> autopyname','module_body_directive',1,'p_module_body_directive','rules.py',1167),
  ('module_body_directive -> docstring','module_body_directive',1,'p_module_body_directive','rules.py',1168),
  ('module_code -> ModuleCode CODE_BLOCK','module_code',2,'p_module_code','rules.py',1176),
  ('pickle_code -> PickleCode CODE_BLOCK','pickle_code',2,'p_pickle_code','rules.py',1189),
  ('platforms -> Platforms { qualifier_list }','platforms',4,'p_platforms','rules.py',1205),
  ('plugin -> Plugin NAME','plugin',2,'p_plugin','rules.py',1229),
  ('postinit_code -> PostInitialisationCode CODE_BLOCK','postinit_code',2,'p_postinit_code','rules.py',1242),
  ('preinit_code -> PreInitialisationCode CODE_BLOCK','preinit_code',2,'p_preinit_code','rules.py',1255),
  ('property -> Property begin_args ( property_args end_args ) opt_property_body','property',7,'p_property','rules.py',1268),
  ('property_args -> property_arg','property_args',1,'p_property_args','rules.py',1303),
  ('property_args -> property_args , property_arg','property_args',3,'p_property_args','rules.py',1304),
  ('property_arg -> get = NAME','property_arg',3,'p_property_arg','rules.py',1313),
  ('property_arg -> name = NAME','property_arg',3,'p_property_arg','rules.py',1314),
  ('property_arg -> set = NAME','property_arg',3,'p_property_arg','rules.py',1315),
  ('opt_property_body -> empty','opt_property_body',1,'p_opt_property_body','rules.py',1321),
  ('opt_property_body -> { property_body } ;','opt_property_body',4,'p_opt_property_body','rules.py',1322),
  ('property_body -> property_line','property_body',1,'p_property_body','rules.py',1328),
  ('property_body -> property_body property_line','property_body',2,'p_property_body','rules.py',1329),
  ('property_line -> if_start','property_line',1,'p_property_line','rules.py',1345),
  ('property_line -> if_end','property_line',1,'p_property_line','rules.py',1346),
  ('property_line -> docstring','property_line',1,'p_property_line','rules.py',1347),
  ('release_code -> ReleaseCode CODE_BLOCK','release_code',2,'p_release_code','rules.py',1355),
  ('timeline -> Timeline { qualifier_list }','timeline',4,'p_timeline','rules.py',1371),
  ('type_code -> TypeCode CODE_BLOCK','type_code',2,'p_type_code','rules.py',1401),
  ('type_header_code -> TypeHeaderCode CODE_BLOCK','type_header_code',2,'p_type_header_code','rules.py',1414),
  ('type_hint_code -> TypeHintCode CODE_BLOCK','type_hint_code',2,'p_type_hint_code','rules.py',1427),
  ('unit_code -> UnitCode CODE_BLOCK','unit_code',2,'p_unit_code','rules.py',1450),
  ('unit_postinclude_code -> UnitPostIncludeCode CODE_BLOCK','unit_postinclude_code',2,'p_unit_postinclude_code','rules.py',1463),
  ('virtual_error_handler -> VirtualErrorHandler NAME CODE_BLOCK','virtual_error_handler',3,'p_virtual_error_handler','rules.py',1476),
  ('virtual_error_handler -> VirtualErrorHandler begin_args ( veh_args end_args ) CODE_BLOCK','virtual_error_handler',7,'p_virtual_error_handler','rules.py',1477),
  ('veh_args -> veh_arg','veh_args',1,'p_veh_args','rules.py',1507),
  ('veh_args -> veh_args , veh_arg','veh_args',3,'p_veh_args','rules.py',1508),
  ('veh_arg -> name = NAME','veh_arg',3,'p_veh_arg','rules.py',1517),
  ('cpp_type -> const base_type derefs opt_ref','cpp_type',4,'p_cpp_type','rules.py',1525),
  ('cpp_type -> base_type derefs opt_ref','cpp_type',3,'p_cpp_type','rules.py',1526),
  ('base_type -> pod_type','base_type',1,'p_base_type','rules.py',1548),
  ('base_type -> scoped_name','base_type',1,'p_base_type','rules.py',1549),
  ('base_type -> scoped_name < cpp_types >','base_type',4,'p_base_type','rules.py',1550),
  ('base_type -> struct scoped_name','base_type',2,'p_base_type','rules.py',1551),
  ('base_type -> union scoped_name','base_type',2,'p_base_type','rules.py',1552),
  ('pod_type -> unsigned long long','pod_type',3,'p_pod_type','rules.py',1627),
  ('pod_type -> signed char','pod_type',2,'p_pod_type','rules.py',1628),
  ('pod_type -> long long','pod_type',2,'p_pod_type','rules.py',1629),
  ('pod_type -> unsigned char','pod_type',2,'p_pod_type','rules.py',1630),
  ('pod_type -> unsigned short','pod_type',2,'p_pod_type','rules.py',1631),
  ('pod_type -> unsigned int','pod_type',2,'p_pod_type','rules.py',1632),
  ('pod_type -> unsigned long','pod_type',2,'p_pod_type','rules.py',1633),
  ('pod_type -> unsigned','pod_type',1,'p_pod_type','rules.py',1634),
  ('pod_type -> short','pod_type',1,'p_pod_type','rules.py',1635),
  ('pod_type -> int','pod_type',1,'p_pod_type','rules.py',1636),
  ('pod_type -> long','pod_type',1,'p_pod_type','rules.py',1637),
  ('pod_type -> float','pod_type',1,'p_pod_type','rules.py',1638),
  ('pod_type -> double','pod_type',1,'p_pod_type','rules.py',1639),
  ('pod_type -> bool','pod_type',1,'p_pod_type','rules.py',1640),
  ('pod_type -> char','pod_type',1,'p_pod_type','rules.py',1641),
  ('pod_type -> wchar_t','pod_type',1,'p_pod_type','rules.py',1642),
  ('pod_type -> void','pod_type',1,'p_pod_type','rules.py',1643),
  ('pod_type -> SIP_PYOBJECT','pod_type',1,'p_pod_type','rules.py',1644),
  ('pod_type -> SIP_PYTUPLE','pod_type',1,'p_pod_type','rules.py',1645),
  ('pod_type -> SIP_PYLIST','pod_type',1,'p_pod_type','rules.py',1646),
  ('pod_type -> SIP_PYDICT','pod_type',1,'p_pod_type','rules.py',1647),
  ('pod_type -> SIP_PYCALLABLE','pod_type',1,'p_pod_type','rules.py',1648),
  ('pod_type -> SIP_PYSLICE','pod_type',1,'p_pod_type','rules.py',1649),
  ('pod_type -> SIP_PYTYPE','pod_type',1,'p_pod_type','rules.py',1650),
  ('pod_type -> SIP_PYBUFFER','pod_type',1,'p_pod_type','rules.py',1651),
  ('pod_type -> SIP_PYENUM','pod_type',1,'p_pod_type','rules.py',1652),
  ('pod_type -> SIP_SSIZE_T','pod_type',1,'p_pod_type','rules.py',1653),
  ('pod_type -> Py_hash_t','pod_type',1,'p_pod_type','rules.py',1654),
  ('pod_type -> Py_ssize_t','pod_type',1,'p_pod_type','rules.py',1655),
  ('pod_type -> size_t','pod_type',1,'p_pod_type','rules.py',1656),
  ('pod_type -> ELLIPSIS','pod_type',1,'p_pod_type','rules.py',1657),
  ('cpp_types -> cpp_type','cpp_types',1,'p_cpp_types','rules.py',1673),
  ('cpp_types -> cpp_types , cpp_type','cpp_types',3,'p_cpp_types','rules.py',1674),
  ('derefs -> empty','derefs',1,'p_derefs','rules.py',1688),
  ('derefs -> derefs *','derefs',2,'p_derefs','rules.py',1689),
  ('derefs -> derefs * const','derefs',3,'p_derefs','rules.py',1690),
  ('opt_ref -> &','opt_ref',1,'p_opt_ref','rules.py',1700),
  ('opt_ref -> empty','opt_ref',1,'p_opt_ref','rules.py',1701),
  ('class_template -> template_decl class_decl','class_template',2,'p_class_template','rules.py',1741),
  ('class_docstring -> docstring','class_docstring',1,'p_class_docstring','rules.py',1756),
  ('class_decl -> class class_head opt_class_definition ;','class_decl',4,'p_class_decl','rules.py',1771),
  ('class_head -> scoped_name superclasses opt_annos','class_head',3,'p_class_head','rules.py',1783),
  ('struct_decl -> struct struct_head opt_class_definition ;','struct_decl',4,'p_struct_decl','rules.py',1802),
  ('struct_head -> scoped_name superclasses opt_annos','struct_head',3,'p_struct_head','rules.py',1813),
  ('superclasses -> : superclass_list','superclasses',2,'p_superclasses','rules.py',1829),
  ('superclasses -> empty','superclasses',1,'p_superclasses','rules.py',1830),
  ('superclass_list -> superclass','superclass_list',1,'p_superclass_list','rules.py',1836),
  ('superclass_list -> superclass_list , superclass','superclass_list',3,'p_superclass_list','rules.py',1837),
  ('superclass -> class_access scoped_name','superclass',2,'p_superclass','rules.py',1853),
  ('class_access -> empty','class_access',1,'p_class_access','rules.py',1881),
  ('class_access -> public','class_access',1,'p_class_access','rules.py',1882),
  ('class_access -> protected','class_access',1,'p_class_access','rules.py',1883),
  ('class_access -> private','class_access',1,'p_class_access','rules.py',1884),
  ('opt_class_definition -> { opt_class_body }','opt_class_definition',3,'p_opt_class_definition','rules.py',1893),
  ('opt_class_definition -> empty','opt_class_definition',1,'p_opt_class_definition','rules.py',1894),
  ('opt_class_body -> class_body','opt_class_body',1,'p_opt_class_body','rules.py',1905),
  ('opt_class_body -> empty','opt_class_body',1,'p_opt_class_body','rules.py',1906),
  ('class_body -> class_line','class_body',1,'p_class_body','rules.py',1910),
  ('class_body -> class_body class_line','class_body',2,'p_class_body','rules.py',1911),
  ('class_line -> if_start','class_line',1,'p_class_line','rules.py',1915),
  ('class_line -> if_end','class_line',1,'p_class_line','rules.py',1916),
  ('class_line -> class_decl','class_line',1,'p_class_line','rules.py',1917),
  ('class_line -> class_docstring','class_line',1,'p_class_line','rules.py',1918),
  ('class_line -> class_template','class_line',1,'p_class_line','rules.py',1919),
  ('class_line -> ctor','class_line',1,'p_class_line','rules.py',1920),
  ('class_line -> dtor','class_line',1,'p_class_line','rules.py',1921),
  ('class_line -> enum_decl','class_line',1,'p_class_line','rules.py',1922),
  ('class_line -> exception','class_line',1,'p_class_line','rules.py',1923),
  ('class_line -> typedef_decl','class_line',1,'p_class_line','rules.py',1924),
  ('class_line -> method_variable','class_line',1,'p_class_line','rules.py',1925),
  ('class_line -> namespace_decl','class_line',1,'p_class_line','rules.py',1926),
  ('class_line -> struct_decl','class_line',1,'p_class_line','rules.py',1927),
  ('class_line -> union_decl','class_line',1,'p_class_line','rules.py',1928),
  ('class_line -> public_specifier','class_line',1,'p_class_line','rules.py',1929),
  ('class_line -> protected_specifier','class_line',1,'p_class_line','rules.py',1930),
  ('class_line -> private_specifier','class_line',1,'p_class_line','rules.py',1931),
  ('class_line -> signals_specifier','class_line',1,'p_class_line','rules.py',1932),
  ('class_line -> convert_from_type_code','class_line',1,'p_class_line','rules.py',1933),
  ('class_line -> convert_to_subclass_code','class_line',1,'p_class_line','rules.py',1934),
  ('class_line -> convert_to_type_code','class_line',1,'p_class_line','rules.py',1935),
  ('class_line -> finalisation_code','class_line',1,'p_class_line','rules.py',1936),
  ('class_line -> gc_clear_code','class_line',1,'p_class_line','rules.py',1937),
  ('class_line -> gc_traverse_code','class_line',1,'p_class_line','rules.py',1938),
  ('class_line -> get_buffer_code','class_line',1,'p_class_line','rules.py',1939),
  ('class_line -> instance_code','class_line',1,'p_class_line','rules.py',1940),
  ('class_line -> pickle_code','class_line',1,'p_class_line','rules.py',1941),
  ('class_line -> property','class_line',1,'p_class_line','rules.py',1942),
  ('class_line -> release_buffer_code','class_line',1,'p_class_line','rules.py',1943),
  ('class_line -> type_code','class_line',1,'p_class_line','rules.py',1944),
  ('class_line -> type_header_code','class_line',1,'p_class_line','rules.py',1945),
  ('class_line -> type_hint_code','class_line',1,'p_class_line','rules.py',1946),
  ('class_line -> BIGetReadBufferCode CODE_BLOCK','class_line',2,'p_class_line','rules.py',1947),
  ('class_line -> BIGetWriteBufferCode CODE_BLOCK','class_line',2,'p_class_line','rules.py',1948),
  ('class_line -> BIGetSegCountCode CODE_BLOCK','class_line',2,'p_class_line','rules.py',1949),
  ('class_line -> BIGetCharBufferCode CODE_BLOCK','class_line',2,'p_class_line','rules.py',1950),
  ('ctor -> explicit ctor_decl','ctor',2,'p_ctor','rules.py',1971),
  ('ctor -> ctor_decl','ctor',1,'p_ctor','rules.py',1972),
  ('ctor_decl -> NAME ( opt_arg_list ) opt_exceptions opt_annos opt_ctor_signature ; opt_docstring premethod_code method_code','ctor_decl',11,'p_ctor_decl','rules.py',1984),
  ('opt_ctor_signature -> [ ( opt_arg_list ) ]','opt_ctor_signature',5,'p_opt_ctor_signature','rules.py',1998),
  ('opt_ctor_signature -> empty','opt_ctor_signature',1,'p_opt_ctor_signature','rules.py',1999),
  ('dtor -> opt_virtual ~ NAME ( ) opt_exceptions opt_abstract opt_annos ; premethod_code method_code virtual_catcher_code','dtor',12,'p_dtor','rules.py',2015),
  ('method_variable -> Q_SIGNAL simple_method_variable','method_variable',2,'p_method_variable','rules.py',2032),
  ('method_variable -> Q_SLOT simple_method_variable','method_variable',2,'p_method_variable','rules.py',2033),
  ('method_variable -> simple_method_variable','method_variable',1,'p_method_variable','rules.py',2034),
  ('simple_method_variable -> virtual function','simple_method_variable',2,'p_simple_method_variable','rules.py',2054),
  ('simple_method_variable -> static plain_method_variable','simple_method_variable',2,'p_simple_method_variable','rules.py',2055),
  ('simple_method_variable -> plain_method_variable','simple_method_variable',1,'p_simple_method_variable','rules.py',2056),
  ('plain_method_variable -> function','plain_method_variable',1,'p_plain_method_variable','rules.py',2074),
  ('plain_method_variable -> variable','plain_method_variable',1,'p_plain_method_variable','rules.py',2075),
  ('public_specifier -> public opt_slots :','public_specifier',3,'p_public_specifier','rules.py',2081),
  ('protected_specifier -> protected opt_slots :','protected_specifier',3,'p_protected_specifier','rules.py',2093),
  ('private_specifier -> private opt_slots :','private_specifier',3,'p_private_specifier','rules.py',2105),
  ('signals_specifier -> signals :','signals_specifier',2,'p_signals_specifier','rules.py',2117),
  ('signals_specifier -> Q_SIGNALS :','signals_specifier',2,'p_signals_specifier','rules.py',2118),
  ('opt_slots -> slots','opt_slots',1,'p_opt_slots','rules.py',2130),
  ('opt_slots -> Q_SLOTS','opt_slots',1,'p_opt_slots','rules.py',2131),
  ('opt_slots -> empty','opt_slots',1,'p_opt_slots','rules.py',2132),
  ('enum_decl -> enum opt_enum_key opt_name opt_annos { opt_enum_body } ;','enum_decl',8,'p_enum_decl','rules.py',2155),
  ('opt_enum_key -> class','opt_enum_key',1,'p_opt_enum_key','rules.py',2168),
  ('opt_enum_key -> struct','opt_enum_key',1,'p_opt_enum_key','rules.py',2169),
  ('opt_enum_key -> union','opt_enum_key',1,'p_opt_enum_key','rules.py',2170),
  ('opt_enum_key -> empty','opt_enum_key',1,'p_opt_enum_key','rules.py',2171),
  ('opt_enum_body -> enum_body','opt_enum_body',1,'p_opt_enum_body','rules.py',2178),
  ('opt_enum_body -> empty','opt_enum_body',1,'p_opt_enum_body','rules.py',2179),
  ('enum_body -> enum_line','enum_body',1,'p_enum_body','rules.py',2185),
  ('enum_body -> enum_body enum_line','enum_body',2,'p_enum_body','rules.py',2186),
  ('enum_line -> if_start','enum_line',1,'p_enum_line','rules.py',2200),
  ('enum_line -> if_end','enum_line',1,'p_enum_line','rules.py',2201),
  ('enum_line -> NAME opt_enum_assign opt_annos opt_comma','enum_line',4,'p_enum_line','rules.py',2202),
  ('opt_enum_assign -> = value','opt_enum_assign',2,'p_opt_enum_assign','rules.py',2220),
  ('opt_enum_assign -> empty','opt_enum_assign',1,'p_opt_enum_assign','rules.py',2221),
  ('opt_comma -> empty','opt_comma',1,'p_opt_comma','rules.py',2225),
  ('opt_comma -> ,','opt_comma',1,'p_opt_comma','rules.py',2226),
  ('exception -> Exception scoped_name opt_base_exception opt_annos { exception_body } ;','exception',8,'p_exception','rules.py',2239),
  ('opt_base_exception -> ( scoped_name )','opt_base_exception',3,'p_opt_base_exception','rules.py',2287),
  ('opt_base_exception -> empty','opt_base_exception',1,'p_opt_base_exception','rules.py',2288),
  ('exception_body -> exception_line','exception_body',1,'p_exception_body','rules.py',2315),
  ('exception_body -> exception_body exception_line','exception_body',2,'p_exception_body','rules.py',2316),
  ('exception_line -> if_start','exception_line',1,'p_exception_line','rules.py',2325),
  ('exception_line -> if_end','exception_line',1,'p_exception_line','rules.py',2326),
  ('exception_line -> RaiseCode CODE_BLOCK','exception_line',2,'p_exception_line','rules.py',2327),
  ('exception_line -> TypeHeaderCode CODE_BLOCK','exception_line',2,'p_exception_line','rules.py',2328),
  ('function -> function_decl','function',1,'p_function','rules.py',2374),
  ('function -> assignment_operator_decl','function',1,'p_function','rules.py',2375),
  ('function -> operator_decl','function',1,'p_function','rules.py',2376),
  ('function -> operator_cast_decl','function',1,'p_function','rules.py',2377),
  ('function_decl -> cpp_type NAME ( opt_arg_list ) opt_const opt_final opt_exceptions opt_abstract opt_annos opt_signature ; opt_docstring premethod_code method_code virtual_catcher_code virtual_call_code','function_decl',17,'p_function_decl','rules.py',2383),
  ('assignment_operator_decl -> cpp_type operator = ( cpp_type ) ;','assignment_operator_decl',7,'p_assignment_operator_decl','rules.py',2400),
  ('operator_decl -> cpp_type operator operator_name ( opt_arg_list ) opt_const opt_final opt_exceptions opt_abstract opt_annos opt_signature ; premethod_code method_code virtual_catcher_code virtual_call_code','operator_decl',17,'p_operator_decl','rules.py',2415),
  ('operator_cast_decl -> operator cpp_type ( opt_arg_list ) opt_const opt_final opt_exceptions opt_abstract opt_annos opt_signature ; premethod_code method_code virtual_catcher_code virtual_call_code','operator_cast_decl',16,'p_operator_cast_decl','rules.py',2467),
  ('opt_arg_list -> arg_list','opt_arg_list',1,'p_opt_arg_list','rules.py',2512),
  ('opt_arg_list -> empty','opt_arg_list',1,'p_opt_arg_list','rules.py',2513),
  ('arg_list -> arg_value','arg_list',1,'p_arg_list','rules.py',2531),
  ('arg_list -> arg_list , arg_value','arg_list',3,'p_arg_list','rules.py',2532),
  ('arg_value -> arg_type opt_assign','arg_value',2,'p_arg_value','rules.py',2544),
  ('arg_type -> cpp_type opt_name opt_annos','arg_type',3,'p_arg_type','rules.py',2577),
  ('opt_assign -> = expr','opt_assign',2,'p_opt_assign','rules.py',2636),
  ('opt_assign -> empty','opt_assign',1,'p_opt_assign','rules.py',2637),
  ('expr -> value','expr',1,'p_expr','rules.py',2643),
  ('expr -> expr binop value','expr',3,'p_expr','rules.py',2644),
  ('value -> opt_cast opt_unop simple_value','value',3,'p_value','rules.py',2657),
  ('simple_value -> empty_value','simple_value',1,'p_simple_value','rules.py',2667),
  ('simple_value -> function_call_value','simple_value',1,'p_simple_value','rules.py',2668),
  ('simple_value -> null_value','simple_value',1,'p_simple_value','rules.py',2669),
  ('simple_value -> number_value','simple_value',1,'p_simple_value','rules.py',2670),
  ('simple_value -> quoted_char_value','simple_value',1,'p_simple_value','rules.py',2671),
  ('simple_value -> real_value','simple_value',1,'p_simple_value','rules.py',2672),
  ('simple_value -> scoped_name_value','simple_value',1,'p_simple_value','rules.py',2673),
  ('simple_value -> string_value','simple_value',1,'p_simple_value','rules.py',2674),
  ('empty_value -> { }','empty_value',2,'p_empty_value','rules.py',2680),
  ('function_call_value -> base_type ( opt_expr_list )','function_call_value',4,'p_function_call_value','rules.py',2686),
  ('null_value -> NULL','null_value',1,'p_null_value','rules.py',2692),
  ('number_value -> NUMBER','number_value',1,'p_number_value','rules.py',2698),
  ('number_value -> bool_value','number_value',1,'p_number_value','rules.py',2699),
  ('quoted_char_value -> QUOTED_CHAR','quoted_char_value',1,'p_quoted_char_value','rules.py',2705),
  ('real_value -> REAL','real_value',1,'p_real_value','rules.py',2711),
  ('scoped_name_value -> scoped_name','scoped_name_value',1,'p_scoped_name_value','rules.py',2717),
  ('string_value -> STRING','string_value',1,'p_string_value','rules.py',2723),
  ('opt_expr_list -> expr_list','opt_expr_list',1,'p_opt_expr_list','rules.py',2729),
  ('opt_expr_list -> empty','opt_expr_list',1,'p_opt_expr_list','rules.py',2730),
  ('expr_list -> expr','expr_list',1,'p_expr_list','rules.py',2736),
  ('expr_list -> expr_list , expr','expr_list',3,'p_expr_list','rules.py',2737),
  ('opt_cast -> ( scoped_name )','opt_cast',3,'p_opt_cast','rules.py',2749),
  ('opt_cast -> empty','opt_cast',1,'p_opt_cast','rules.py',2750),
  ('binop -> -','binop',1,'p_binop','rules.py',2756),
  ('binop -> +','binop',1,'p_binop','rules.py',2757),
  ('binop -> *','binop',1,'p_binop','rules.py',2758),
  ('binop -> /','binop',1,'p_binop','rules.py',2759),
  ('binop -> &','binop',1,'p_binop','rules.py',2760),
  ('binop -> |','binop',1,'p_binop','rules.py',2761),
  ('opt_unop -> empty','opt_unop',1,'p_opt_unop','rules.py',2767),
  ('opt_unop -> !','opt_unop',1,'p_opt_unop','rules.py',2768),
  ('opt_unop -> ~','opt_unop',1,'p_opt_unop','rules.py',2769),
  ('opt_unop -> -','opt_unop',1,'p_opt_unop','rules.py',2770),
  ('opt_unop -> +','opt_unop',1,'p_opt_unop','rules.py',2771),
  ('opt_unop -> *','opt_unop',1,'p_opt_unop','rules.py',2772),
  ('opt_unop -> &','opt_unop',1,'p_opt_unop','rules.py',2773),
  ('opt_exceptions -> empty','opt_exceptions',1,'p_opt_exceptions','rules.py',2779),
  ('opt_exceptions -> noexcept','opt_exceptions',1,'p_opt_exceptions','rules.py',2780),
  ('opt_exceptions -> throw ( opt_exception_list )','opt_exceptions',4,'p_opt_exceptions','rules.py',2781),
  ('opt_exception_list -> exception_list','opt_exception_list',1,'p_opt_exception_list','rules.py',2792),
  ('opt_exception_list -> empty','opt_exception_list',1,'p_opt_exception_list','rules.py',2793),
  ('exception_list -> scoped_name','exception_list',1,'p_exception_list','rules.py',2799),
  ('exception_list -> exception_list , scoped_name','exception_list',3,'p_exception_list','rules.py',2800),
  ('opt_abstract -> = NUMBER','opt_abstract',2,'p_opt_abstract','rules.py',2814),
  ('opt_abstract -> empty','opt_abstract',1,'p_opt_abstract','rules.py',2815),
  ('opt_signature -> [ cpp_type ( opt_arg_list ) ]','opt_signature',6,'p_opt_signature','rules.py',2827),
  ('opt_signature -> empty','opt_signature',1,'p_opt_signature','rules.py',2828),
  ('operator_name -> +','operator_name',1,'p_operator_name','rules.py',2870),
  ('operator_name -> -','operator_name',1,'p_operator_name','rules.py',2871),
  ('operator_name -> *','operator_name',1,'p_operator_name','rules.py',2872),
  ('operator_name -> /','operator_name',1,'p_operator_name','rules.py',2873),
  ('operator_name -> %','operator_name',1,'p_operator_name','rules.py',2874),
  ('operator_name -> &','operator_name',1,'p_operator_name','rules.py',2875),
  ('operator_name -> |','operator_name',1,'p_operator_name','rules.py',2876),
  ('operator_name -> ^','operator_name',1,'p_operator_name','rules.py',2877),
  ('operator_name -> < <','operator_name',2,'p_operator_name','rules.py',2878),
  ('operator_name -> > >','operator_name',2,'p_operator_name','rules.py',2879),
  ('operator_name -> + =','operator_name',2,'p_operator_name','rules.py',2880),
  ('operator_name -> - =','operator_name',2,'p_operator_name','rules.py',2881),
  ('operator_name -> * =','operator_name',2,'p_operator_name','rules.py',2882),
  ('operator_name -> / =','operator_name',2,'p_operator_name','rules.py',2883),
  ('operator_name -> % =','operator_name',2,'p_operator_name','rules.py',2884),
  ('operator_name -> & =','operator_name',2,'p_operator_name','rules.py',2885),
  ('operator_name -> | =','operator_name',2,'p_operator_name','rules.py',2886),
  ('operator_name -> ^ =','operator_name',2,'p_operator_name','rules.py',2887),
  ('operator_name -> < < =','operator_name',3,'p_operator_name','rules.py',2888),
  ('operator_name -> > > =','operator_name',3,'p_operator_name','rules.py',2889),
  ('operator_name -> ~','operator_name',1,'p_operator_name','rules.py',2890),
  ('operator_name -> ( )','operator_name',2,'p_operator_name','rules.py',2891),
  ('operator_name -> [ ]','operator_name',2,'p_operator_name','rules.py',2892),
  ('operator_name -> <','operator_name',1,'p_operator_name','rules.py',2893),
  ('operator_name -> < =','operator_name',2,'p_operator_name','rules.py',2894),
  ('operator_name -> = =','operator_name',2,'p_operator_name','rules.py',2895),
  ('operator_name -> ! =','operator_name',2,'p_operator_name','rules.py',2896),
  ('operator_name -> >','operator_name',1,'p_operator_name','rules.py',2897),
  ('operator_name -> > =','operator_name',2,'p_operator_name','rules.py',2898),
  ('method_code -> MethodCode CODE_BLOCK','method_code',2,'p_method_code','rules.py',2904),
  ('method_code -> empty','method_code',1,'p_method_code','rules.py',2905),
  ('premethod_code -> PreMethodCode CODE_BLOCK','premethod_code',2,'p_premethod_code','rules.py',2911),
  ('premethod_code -> empty','premethod_code',1,'p_premethod_code','rules.py',2912),
  ('virtual_call_code -> VirtualCallCode CODE_BLOCK','virtual_call_code',2,'p_virtual_call_code','rules.py',2918),
  ('virtual_call_code -> empty','virtual_call_code',1,'p_virtual_call_code','rules.py',2919),
  ('virtual_catcher_code -> VirtualCatcherCode CODE_BLOCK','virtual_catcher_code',2,'p_virtual_catcher_code','rules.py',2925),
  ('virtual_catcher_code -> empty','virtual_catcher_code',1,'p_virtual_catcher_code','rules.py',2926),
  ('namespace_decl -> namespace namespace_head opt_namespace_body ;','namespace_decl',4,'p_namespace_decl','rules.py',2940),
  ('namespace_head -> scoped_name opt_annos','namespace_head',2,'p_namespace_head','rules.py',2955),
  ('opt_namespace_body -> { namespace_body }','opt_namespace_body',3,'p_opt_namespace_body','rules.py',2973),
  ('opt_namespace_body -> empty','opt_namespace_body',1,'p_opt_namespace_body','rules.py',2974),
  ('namespace_body -> namespace_statement','namespace_body',1,'p_namespace_body','rules.py',2978),
  ('namespace_body -> namespace_body namespace_statement','namespace_body',2,'p_namespace_body','rules.py',2979),
  ('typedef_decl -> typedef cpp_type NAME opt_annos ; opt_docstring','typedef_decl',6,'p_typedef_decl','rules.py',2998),
  ('typedef_decl -> typedef cpp_type ( * NAME ) ( cpp_types ) opt_annos ; opt_docstring','typedef_decl',12,'p_typedef_decl','rules.py',2999),
  ('union_decl -> union union_head opt_class_definition ;','union_decl',4,'p_union_decl','rules.py',3078),
  ('union_head -> scoped_name opt_annos','union_head',2,'p_union_head','rules.py',3089),
  ('variable -> cpp_type NAME opt_annos variable_body ;','variable',5,'p_variable','rules.py',3119),
  ('variable_body -> { variable_body_directives }','variable_body',3,'p_variable_body','rules.py',3161),
  ('variable_body -> empty','variable_body',1,'p_variable_body','rules.py',3162),
  ('variable_body_directives -> variable_body_directive','variable_body_directives',1,'p_variable_body_directives','rules.py',3168),
  ('variable_body_directives -> variable_body_directives variable_body_directive','variable_body_directives',2,'p_variable_body_directives','rules.py',3169),
  ('variable_body_directive -> if_start','variable_body_directive',1,'p_variable_body_directive','rules.py',3178),
  ('variable_body_directive -> if_end','variable_body_directive',1,'p_variable_body_directive','rules.py',3179),
  ('variable_body_directive -> AccessCode CODE_BLOCK','variable_body_directive',2,'p_variable_body_directive','rules.py',3180),
  ('variable_body_directive -> GetCode CODE_BLOCK','variable_body_directive',2,'p_variable_body_directive','rules.py',3181),
  ('variable_body_directive -> SetCode CODE_BLOCK','variable_body_directive',2,'p_variable_body_directive','rules.py',3182),
  ('opt_annos -> / annotations /','opt_annos',3,'p_opt_annos','rules.py',3190),
  ('opt_annos -> empty','opt_annos',1,'p_opt_annos','rules.py',3191),
  ('annotations -> annotation','annotations',1,'p_annotations','rules.py',3197),
  ('annotations -> annotations , annotation','annotations',3,'p_annotations','rules.py',3198),
  ('annotation -> NAME','annotation',1,'p_annotation','rules.py',3207),
  ('annotation -> NAME = annotation_value','annotation',3,'p_annotation','rules.py',3208),
  ('annotation_value -> dotted_name','annotation_value',1,'p_annotation_value','rules.py',3217),
  ('annotation_value -> STRING','annotation_value',1,'p_annotation_value','rules.py',3218),
  ('annotation_value -> NUMBER','annotation_value',1,'p_annotation_value','rules.py',3219),
  ('scoped_name -> SCOPE relative_scoped_name','scoped_name',2,'p_scoped_name','rules.py',3235),
  ('scoped_name -> relative_scoped_name','scoped_name',1,'p_scoped_name','rules.py',3236),
  ('relative_scoped_name -> NAME','relative_scoped_name',1,'p_relative_scoped_name','rules.py',3247),
  ('relative_scoped_name -> relative_scoped_name SCOPE NAME','relative_scoped_name',3,'p_relative_scoped_name','rules.py',3248),
  ('template_decl -> template < cpp_types >','template_decl',4,'p_template_decl','rules.py',3261),
  ('bool_value -> true','bool_value',1,'p_bool_value','rules.py',3274),
  ('bool_value -> True','bool_value',1,'p_bool_value','rules.py',3275),
  ('bool_value -> false','bool_value',1,'p_bool_value','rules.py',3276),
  ('bool_value -> False','bool_value',1,'p_bool_value','rules.py',3277),
  ('dotted_name -> NAME','dotted_name',1,'p_dotted_name','rules.py',3283),
  ('dotted_name -> DOTTED_NAME','dotted_name',1,'p_dotted_name','rules.py',3284),
  ('file_path -> NAME','file_path',1,'p_file_path','rules.py',3290),
  ('file_path -> DOTTED_NAME','file_path',1,'p_file_path','rules.py',3291),
  ('file_path -> FILE_PATH','file_path',1,'p_file_path','rules.py',3292),
  ('empty -> <empty>','empty',0,'p_empty','rules.py',3298),
  ('opt_const -> const','opt_const',1,'p_opt_const','rules.py',3304),
  ('opt_const -> empty','opt_const',1,'p_opt_const','rules.py',3305),
  ('opt_docstring -> docstring','opt_docstring',1,'p_opt_docstring','rules.py',3311),
  ('opt_docstring -> empty','opt_docstring',1,'p_opt_docstring','rules.py',3312),
  ('opt_final -> final','opt_final',1,'p_opt_final','rules.py',3318),
  ('opt_final -> empty','opt_final',1,'p_opt_final','rules.py',3319),
  ('opt_name -> NAME','opt_name',1,'p_opt_name','rules.py',3325),
  ('opt_name -> empty','opt_name',1,'p_opt_name','rules.py',3326),
  ('opt_virtual -> virtual','opt_virtual',1,'p_opt_virtual','rules.py',3332),
  ('opt_virtual -> empty','opt_virtual',1,'p_opt_virtual','rules.py',3333),
  ('ored_qualifiers -> NAME','ored_qualifiers',1,'p_ored_qualifiers','rules.py',3345),
  ('ored_qualifiers -> ! NAME','ored_qualifiers',2,'p_ored_qualifiers','rules.py',3346),
  ('ored_qualifiers -> ored_qualifiers LOGICAL_OR NAME','ored_qualifiers',3,'p_ored_qualifiers','rules.py',3347),
  ('ored_qualifiers -> ored_qualifiers LOGICAL_OR ! NAME','ored_qualifiers',4,'p_ored_qualifiers','rules.py',3348),
  ('qualifier_list -> NAME','qualifier_list',1,'p_qualifier_list','rules.py',3365),
  ('qualifier_list -> qualifier_list NAME','qualifier_list',2,'p_qualifier_list','rules.py',3366),
  ('qualifiers -> ored_qualifiers','qualifiers',1,'p_qualifiers','rules.py',3378),
  ('qualifiers -> opt_name - opt_name','qualifiers',3,'p_qualifiers','rules.py',3379),
]
