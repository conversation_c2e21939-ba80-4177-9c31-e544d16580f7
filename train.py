# -*- coding: utf-8 -*-
"""
YOLOv8昆虫检测模型训练脚本
Train YOLOv8 model for insect detection
"""

import os
import sys
from ultralytics import YOLO
import torch
import yaml

def check_dataset(data_path):
    """检查数据集配置和文件"""
    print("=" * 50)
    print("检查数据集配置...")
    
    if not os.path.exists(data_path):
        print(f"❌ 数据集配置文件不存在: {data_path}")
        return False
    
    # 读取配置文件
    with open(data_path, 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    
    # 检查必要的配置项
    required_keys = ['train', 'val', 'nc', 'names']
    for key in required_keys:
        if key not in data_config:
            print(f"❌ 配置文件缺少必要项: {key}")
            return False
    
    # 检查数据集目录
    train_dir = data_config['train']
    val_dir = data_config['val']
    
    if not os.path.exists(train_dir):
        print(f"❌ 训练集目录不存在: {train_dir}")
        return False
    
    if not os.path.exists(val_dir):
        print(f"❌ 验证集目录不存在: {val_dir}")
        return False
    
    # 统计数据集文件数量
    train_images = len([f for f in os.listdir(train_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    val_images = len([f for f in os.listdir(val_dir) if f.lower().endswith(('.jpg', '.jpeg', '.png'))])
    
    print(f"✅ 数据集配置检查通过")
    print(f"📊 训练集图片数量: {train_images}")
    print(f"📊 验证集图片数量: {val_images}")
    print(f"📊 类别数量: {data_config['nc']}")
    
    return True

def setup_training_environment():
    """设置训练环境"""
    print("=" * 50)
    print("设置训练环境...")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        device_name = torch.cuda.get_device_name(current_device)
        print(f"✅ CUDA可用 - 设备数量: {device_count}")
        print(f"🎯 当前设备: {device_name}")
        return 'cuda'
    else:
        print("⚠️  CUDA不可用，使用CPU训练")
        print("💡 建议: 安装CUDA版本的PyTorch以获得更好的训练速度")
        return 'cpu'

def train_model(model_path, data_path, epochs=500, batch_size=64, device='auto'):
    """训练YOLOv8模型"""
    print("=" * 50)
    print("开始训练YOLOv8模型...")
    
    try:
        # 加载预训练模型
        print(f"📥 加载预训练模型: {model_path}")
        model = YOLO(model_path)
        
        # 训练参数配置
        training_args = {
            'data': data_path,           # 数据集配置文件
            'epochs': epochs,            # 训练轮数
            'batch': batch_size,         # 批次大小
            'device': device,            # 训练设备
            'workers': 0,                # 数据加载进程数（Windows建议设为0）
            'project': 'runs/detect',    # 项目保存目录
            'name': 'exp',               # 实验名称
            'save': True,                # 保存检查点
            'save_period': 50,           # 每50轮保存一次
            'cache': False,              # 不缓存图像到内存
            'imgsz': 640,                # 输入图像尺寸
            'optimizer': 'SGD',          # 优化器
            'lr0': 0.01,                 # 初始学习率
            'lrf': 0.01,                 # 最终学习率
            'momentum': 0.937,           # 动量
            'weight_decay': 0.0005,      # 权重衰减
            'warmup_epochs': 3,          # 预热轮数
            'warmup_momentum': 0.8,      # 预热动量
            'warmup_bias_lr': 0.1,       # 预热偏置学习率
            'box': 7.5,                  # 边界框损失权重
            'cls': 0.5,                  # 分类损失权重
            'dfl': 1.5,                  # DFL损失权重
            'pose': 12.0,                # 姿态损失权重
            'kobj': 2.0,                 # 关键点目标损失权重
            'label_smoothing': 0.0,      # 标签平滑
            'nbs': 64,                   # 标准批次大小
            'overlap_mask': True,        # 重叠掩码
            'mask_ratio': 4,             # 掩码比例
            'dropout': 0.0,              # Dropout率
            'val': True,                 # 验证
            'plots': True,               # 保存训练图表
            'verbose': True,             # 详细输出
        }
        
        print("🚀 训练参数配置:")
        for key, value in training_args.items():
            print(f"   {key}: {value}")
        
        print("\n🎯 开始训练...")
        print("=" * 50)
        
        # 开始训练
        results = model.train(**training_args)
        
        print("=" * 50)
        print("✅ 训练完成!")
        print(f"📁 模型保存路径: runs/detect/exp/weights/")
        print(f"🏆 最佳模型: runs/detect/exp/weights/best.pt")
        print(f"📊 最终模型: runs/detect/exp/weights/last.pt")
        
        return results
        
    except Exception as e:
        print(f"❌ 训练过程中出现错误: {str(e)}")
        return None

def main():
    """主函数"""
    print("YOLOv8昆虫检测模型训练")
    print("=" * 50)
    
    # 配置参数
    model_path = 'yolov8s.pt'        # 预训练模型路径
    data_path = 'datasets/data.yaml'  # 数据集配置文件路径
    epochs = 500                      # 训练轮数
    batch_size = 64                   # 批次大小
    
    # 检查数据集
    if not check_dataset(data_path):
        print("❌ 数据集检查失败，请检查数据集配置和文件")
        sys.exit(1)
    
    # 设置训练环境
    device = setup_training_environment()
    
    # 如果是CPU训练，建议减小批次大小
    if device == 'cpu':
        batch_size = min(batch_size, 16)
        print(f"💡 CPU训练建议批次大小: {batch_size}")
    
    # 开始训练
    results = train_model(
        model_path=model_path,
        data_path=data_path,
        epochs=epochs,
        batch_size=batch_size,
        device=device
    )
    
    if results:
        print("\n🎉 训练任务完成!")
        print("📝 查看训练结果:")
        print("   - 训练日志: runs/detect/exp/")
        print("   - 训练图表: runs/detect/exp/results.png")
        print("   - 验证图片: runs/detect/exp/val_batch*.jpg")
        print("   - 混淆矩阵: runs/detect/exp/confusion_matrix.png")
    else:
        print("❌ 训练失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
