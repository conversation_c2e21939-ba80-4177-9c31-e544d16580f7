# 昆虫检测数据集说明

## 数据集结构

```
datasets/
├── data.yaml              # 数据集配置文件
├── images/                # 图片文件夹
│   ├── train/            # 训练集图片
│   ├── val/              # 验证集图片
│   └── test/             # 测试集图片
└── labels/               # 标签文件夹
    ├── train/            # 训练集标签
    ├── val/              # 验证集标签
    └── test/             # 测试集标签
```

## 支持的昆虫类别

| 索引 | 英文名称 | 中文名称 | 描述 |
|------|----------|----------|------|
| 0 | army worm | 粘虫 | 重要的农业害虫 |
| 1 | legume blister beetle | 豆芫菁 | 豆类作物害虫 |
| 2 | red spider | 红蜘蛛 | 常见的植物害虫 |
| 3 | rice gall midge | 稻瘿蚊 | 水稻害虫 |
| 4 | rice leaf roller | 稻纵卷叶螟 | 水稻叶片害虫 |
| 5 | rice leafhopper | 稻飞虱 | 水稻刺吸式害虫 |
| 6 | rice water weevil | 稻水象甲 | 水稻根部害虫 |
| 7 | wheat phloeothrips | 麦长管蚜 | 小麦害虫 |
| 8 | white backed plant hopper | 白背飞虱 | 水稻害虫 |
| 9 | yellow rice borer | 黄稻螟 | 水稻钻蛀性害虫 |

## 数据准备步骤

### 1. 收集图片数据

- **训练集**: 建议每个类别至少100-500张图片
- **验证集**: 建议每个类别至少20-100张图片
- **测试集**: 建议每个类别至少10-50张图片

### 2. 图片要求

- **格式**: JPG, JPEG, PNG
- **分辨率**: 建议不低于640x640像素
- **质量**: 清晰，无模糊
- **多样性**: 不同角度、光照、背景

### 3. 标注格式

使用YOLO格式进行标注，每张图片对应一个同名的.txt文件：

```
class_id center_x center_y width height
```

其中：
- `class_id`: 类别索引（0-9）
- `center_x, center_y`: 边界框中心点坐标（相对于图片宽高的比例，0-1）
- `width, height`: 边界框宽高（相对于图片宽高的比例，0-1）

### 4. 标注示例

假设图片尺寸为640x480，昆虫位置为(100,50)到(200,150)：

```
0 0.234375 0.208333 0.15625 0.208333
```

计算过程：
- center_x = (100+200)/2/640 = 0.234375
- center_y = (50+150)/2/480 = 0.208333
- width = (200-100)/640 = 0.15625
- height = (150-50)/480 = 0.208333

## 推荐的标注工具

### 1. LabelImg
- **下载**: https://github.com/tzutalin/labelImg
- **特点**: 简单易用，支持YOLO格式
- **安装**: `pip install labelImg`

### 2. Roboflow
- **网址**: https://roboflow.com/
- **特点**: 在线标注，自动格式转换
- **优势**: 支持数据增强和质量检查

### 3. CVAT
- **网址**: https://cvat.org/
- **特点**: 开源，功能强大
- **适用**: 大规模数据标注项目

## 数据集质量检查

### 1. 图片质量检查
```python
import os
from PIL import Image

def check_images(image_dir):
    for filename in os.listdir(image_dir):
        if filename.lower().endswith(('.jpg', '.jpeg', '.png')):
            try:
                img = Image.open(os.path.join(image_dir, filename))
                print(f"{filename}: {img.size}")
            except Exception as e:
                print(f"错误图片: {filename} - {e}")
```

### 2. 标签格式检查
```python
def check_labels(label_dir):
    for filename in os.listdir(label_dir):
        if filename.endswith('.txt'):
            with open(os.path.join(label_dir, filename), 'r') as f:
                lines = f.readlines()
                for line in lines:
                    parts = line.strip().split()
                    if len(parts) != 5:
                        print(f"标签格式错误: {filename}")
```

## 数据增强建议

在 `data.yaml` 中已配置了基本的数据增强参数：

- **翻转**: 左右翻转概率50%
- **颜色**: HSV色彩空间增强
- **几何**: 平移、缩放变换
- **马赛克**: 多图像拼接增强

## 训练建议

### 1. 数据集划分比例
- **训练集**: 70-80%
- **验证集**: 15-20%
- **测试集**: 5-15%

### 2. 类别平衡
- 确保各类别样本数量相对均衡
- 对于样本较少的类别，可以使用数据增强

### 3. 质量控制
- 定期检查标注质量
- 移除模糊或错误的图片
- 确保标注框准确包围目标

## 常见问题

### Q: 如何处理类别不平衡？
A: 
1. 收集更多少数类别的样本
2. 使用数据增强技术
3. 调整损失函数权重

### Q: 标注框应该多紧？
A: 
1. 紧贴目标边缘，但不要切割
2. 包含完整的昆虫身体
3. 避免包含过多背景

### Q: 如何验证数据集质量？
A: 
1. 使用可视化工具检查标注
2. 训练小规模模型验证
3. 计算数据集统计信息

## 数据集扩展

如需添加新的昆虫类别：

1. 更新 `data.yaml` 中的 `nc` 和 `names`
2. 收集新类别的图片和标注
3. 重新训练模型
4. 更新 `Config.py` 中的类别配置
