# -*- coding: utf-8 -*-
"""
昆虫检测系统配置文件
包含类别名称、颜色配置、路径设置等
"""

import os

# 昆虫类别配置
# 英文类别名称（与模型训练时保持一致）
names = {
    0: 'army worm',                    # 粘虫
    1: 'legume blister beetle',        # 豆芫菁
    2: 'red spider',                   # 红蜘蛛
    3: 'rice gall midge',              # 稻瘿蚊
    4: 'rice leaf roller',             # 稻纵卷叶螟
    5: 'rice leafhopper',              # 稻飞虱
    6: 'rice water weevil',            # 稻水象甲
    7: 'wheat phloeothrips',           # 麦长管蚜
    8: 'white backed plant hopper',    # 白背飞虱
    9: 'yellow rice borer'             # 黄稻螟
}

# 中文类别名称（用于界面显示）
CH_names = {
    0: '粘虫',
    1: '豆芫菁',
    2: '红蜘蛛',
    3: '稻瘿蚊',
    4: '稻纵卷叶螟',
    5: '稻飞虱',
    6: '稻水象甲',
    7: '麦长管蚜',
    8: '白背飞虱',
    9: '黄稻螟'
}

# 类别对应的颜色配置（BGR格式）
class_colors = {
    0: (255, 0, 0),      # 粘虫 - 红色
    1: (0, 255, 0),      # 蚜虫 - 绿色
    2: (0, 0, 255),      # 甲虫 - 蓝色
    3: (255, 255, 0),    # 棉铃虫 - 青色
    4: (255, 0, 255),    # 蚯蚓 - 品红色
    5: (0, 255, 255),    # 蝗虫 - 黄色
    6: (128, 0, 128),    # 蜘蛛 - 紫色
    7: (255, 165, 0),    # 白蚁 - 橙色
    8: (0, 128, 128),    # 蓟马 - 深青色
    9: (128, 128, 0)     # 金针虫 - 橄榄色
}

# 路径配置
# 模型权重文件路径
model_path = 'runs/detect/exp/weights/best.pt'

# 字体文件路径
font_path = 'Font/platech.ttf'

# 结果保存路径
save_path = 'results'

# 示例图片路径
sample_images_path = 'images'

# 确保保存目录存在
if not os.path.exists(save_path):
    os.makedirs(save_path)

# 检测参数配置
# 置信度阈值
confidence_threshold = 0.25

# NMS阈值
nms_threshold = 0.45

# 最大检测数量
max_detections = 1000

# 图像尺寸
img_size = 640

# 设备配置（'cpu' 或 'cuda'）
device = 'cpu'  # 如果有GPU可以改为 'cuda'

# UI配置
# 窗口标题
window_title = "YOLOv8昆虫检测系统"

# 显示区域尺寸
display_width = 700
display_height = 500

# 表格列宽配置
table_column_widths = {
    'id': 80,
    'path': 200,
    'class': 150,
    'confidence': 90,
    'position': 230
}

# 支持的图片格式
supported_image_formats = ['jpg', 'jpeg', 'png', 'bmp']

# 支持的视频格式
supported_video_formats = ['avi', 'mp4', 'mov', 'mkv']

# 版本信息
version = "1.0.0"
author = "昆虫检测系统开发团队"
description = "基于YOLOv8的昆虫识别检测系统"

print(f"配置文件加载完成 - {description} v{version}")
