// qsgabstractrenderer.sip generated by MetaSIP
//
// This file is part of the QtQuick Python extension module.
//
// Copyright (c) 2022 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


%If (Qt_5_4_0 -)

class QSGAbstractRenderer : QObject /NoDefaultCtors/
{
%TypeHeaderCode
#include <qsgabstractrenderer.h>
%End

public:
    enum ClearModeBit
    {
        ClearColorBuffer,
        ClearDepthBuffer,
        ClearStencilBuffer,
    };

    typedef QFlags<QSGAbstractRenderer::ClearModeBit> ClearMode;
%If (Qt_5_14_0 -)

    enum MatrixTransformFlag
    {
        MatrixTransformFlipY,
    };

%End
%If (Qt_5_14_0 -)
    typedef QFlags<QSGAbstractRenderer::MatrixTransformFlag> MatrixTransformFlags;
%End
    virtual ~QSGAbstractRenderer();
    void setDeviceRect(const QRect &rect);
    void setDeviceRect(const QSize &size);
    QRect deviceRect() const;
    void setViewportRect(const QRect &rect);
    void setViewportRect(const QSize &size);
    QRect viewportRect() const;
    void setProjectionMatrixToRect(const QRectF &rect);
%If (Qt_5_14_0 -)
    void setProjectionMatrixToRect(const QRectF &rect, QSGAbstractRenderer::MatrixTransformFlags flags);
%End
    void setProjectionMatrix(const QMatrix4x4 &matrix);
    QMatrix4x4 projectionMatrix() const;
    void setClearColor(const QColor &color);
    QColor clearColor() const;
    void setClearMode(QSGAbstractRenderer::ClearMode mode);
    QSGAbstractRenderer::ClearMode clearMode() const;
    virtual void renderScene(uint fboId = 0) = 0;

signals:
    void sceneGraphChanged();
};

%End
%If (Qt_5_4_0 -)
QFlags<QSGAbstractRenderer::ClearModeBit> operator|(QSGAbstractRenderer::ClearModeBit f1, QFlags<QSGAbstractRenderer::ClearModeBit> f2);
%End
