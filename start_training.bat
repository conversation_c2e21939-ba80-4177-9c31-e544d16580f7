@echo off
chcp 65001 >nul
title YOLOv8昆虫检测模型训练

echo ========================================
echo    YOLOv8昆虫检测模型训练启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

echo [信息] Python环境检查通过
echo.

:: 检查数据集配置
if not exist "datasets\data.yaml" (
    echo [错误] 数据集配置文件不存在: datasets\data.yaml
    echo [提示] 请确保数据集已正确配置
    pause
    exit /b 1
)

echo [信息] 数据集配置文件存在
echo.

:: 检查训练脚本
if not exist "train.py" (
    echo [错误] 训练脚本不存在: train.py
    pause
    exit /b 1
)

echo [信息] 训练脚本检查通过
echo.

:: 检查依赖包
echo [信息] 检查必要的依赖包...
python -c "import ultralytics, torch, yaml" >nul 2>&1
if errorlevel 1 (
    echo [警告] 部分依赖包未安装，正在安装...
    pip install ultralytics torch pyyaml
    if errorlevel 1 (
        echo [错误] 依赖包安装失败
        echo [建议] 手动运行: pip install ultralytics torch pyyaml
        pause
        exit /b 1
    )
)

echo [信息] 依赖包检查通过
echo.

:: 显示训练信息
echo [信息] 训练配置信息:
echo   - 模型: yolov8s.pt
echo   - 数据集: datasets/data.yaml
echo   - 训练轮数: 500
echo   - 批次大小: 64
echo   - 保存路径: runs/detect/exp
echo.

:: 确认开始训练
set /p confirm="确定开始训练吗？(y/n): "
if /i not "%confirm%"=="y" (
    echo [信息] 训练已取消
    pause
    exit /b 0
)

echo.
echo [信息] 开始训练模型...
echo ========================================
echo.

:: 启动训练
python train.py

:: 检查训练结果
if errorlevel 1 (
    echo.
    echo [错误] 训练过程中出现错误
    echo [建议] 检查数据集和配置文件
) else (
    echo.
    echo [成功] 训练完成！
    echo [结果] 模型保存在: runs/detect/exp/weights/
)

echo.
echo 按任意键关闭窗口...
pause >nul
